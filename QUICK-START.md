# Quick Start Guide - Thermal Printer App

## 🚀 Simple Installation (Recommended)

This version works without complex dependencies and is ready to run immediately.

### Prerequisites
- Windows 10/11
- Node.js (any recent version)
- Zonerich AB-88H printer connected and installed

### Installation Steps

1. **Install Electron** (only dependency needed):
   ```bash
   npm install electron@^22.0.0
   ```

2. **Test the setup**:
   ```bash
   npm test
   ```

3. **Run the application**:
   ```bash
   npm start
   ```

   Or for development with DevTools:
   ```bash
   npm run dev
   ```

## 🖨️ Using the Application

### 1. First Launch
- The application will open with a clean interface
- Click "Refresh Printers" to scan for available printers

### 2. Select Your Printer
- Your Zonerich AB-88H printer will be highlighted in green
- Click on it to select it
- The selection will be saved for future use

### 3. Test Print
- Enter a custom message (optional)
- Click "Test Print" to print a comprehensive test document
- The test includes formatting examples and receipt layout

### 4. Monitor Status
- View real-time printer connection status
- Enable auto-refresh for continuous monitoring
- Check printer availability and status

## 🔧 Features

### ✅ Working Features
- **Printer Discovery**: Detects all Windows printers
- **Zonerich Detection**: Automatically identifies Zonerich printers
- **Test Printing**: Prints formatted test documents using Windows print command
- **Status Monitoring**: Real-time printer status updates
- **Settings Persistence**: Remembers selected printer
- **Error Handling**: User-friendly error messages

### 📋 Test Print Contents
The test print includes:
- Header with application info
- Timestamp and printer details
- Custom test message
- Formatting examples
- Alignment demonstrations
- Receipt-style layout
- Footer with completion message

## 🛠️ Troubleshooting

### Printer Not Found
1. Ensure printer is connected (USB/Network)
2. Install printer drivers from manufacturer
3. Verify printer appears in Windows "Printers & scanners"
4. Click "Refresh Printers" in the app

### Print Job Fails
1. Check printer status (paper, power, connectivity)
2. Clear Windows print queue if stuck
3. Ensure no other apps are using the printer
4. Try printing a test page from Windows first

### Application Won't Start
1. Verify Node.js is installed: `node --version`
2. Install Electron: `npm install electron@^22.0.0`
3. Check for error messages in terminal
4. Run with DevTools: `npm run dev`

## 📁 File Structure

```
src/
├── main/
│   └── main-simple.js       # Main Electron process (simplified)
└── renderer/
    ├── index-simple.html    # Frontend HTML
    └── app-simple.js        # Frontend JavaScript
```

## 🔄 Upgrading to Full Version

To upgrade to the full version with Vue.js and advanced thermal printer libraries:

1. **Install additional dependencies**:
   ```bash
   npm install vue@^3.3.4 vite@^4.5.0 @vitejs/plugin-vue@^4.4.0
   npm install node-thermal-printer@^4.4.0 escpos@^3.0.0-alpha.6
   ```

2. **Switch to full version**:
   - Update `package.json` main field to `src/main/main.js`
   - Use the Vue.js components in `src/renderer/`

3. **Run with Vite**:
   ```bash
   npm run electron:dev
   ```

## 💡 Tips

- **Development**: Use `npm run dev` to open DevTools for debugging
- **Performance**: Disable auto-refresh when not needed
- **Testing**: Always test with actual printer before important use
- **Backup**: The app saves printer preferences automatically

## 🆘 Support

### Common Issues
- **"Module not found"**: Run `npm install electron@^22.0.0`
- **"Printer offline"**: Check physical connections and power
- **"Access denied"**: Run terminal as Administrator
- **"Print queue stuck"**: Clear Windows print queue manually

### Getting Help
1. Check the console for error messages (`npm run dev`)
2. Verify printer works with other applications
3. Test with Windows built-in print functionality
4. Review the troubleshooting section above

## 🎯 Next Steps

Once you have the basic version working:
1. Test with your actual Zonerich AB-88H printer
2. Customize the test print content if needed
3. Consider upgrading to the full version for advanced features
4. Integrate with your existing workflow

---

**Note**: This simplified version uses Windows print commands instead of direct thermal printer libraries, making it more compatible but with fewer advanced formatting options. For full ESC/POS command support, upgrade to the complete version once dependencies are resolved.
