// COM口診斷工具
const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔍 COM口診斷工具啟動...\n');

// 1. 檢查串口庫是否可用
console.log('步驟 1: 檢查串口庫...');
let serialportAvailable = false;
try {
  const { SerialPort } = require('serialport');
  console.log('✅ SerialPort庫已安裝並可用');
  serialportAvailable = true;
} catch (error) {
  console.log('❌ SerialPort庫不可用:', error.message);
}

// 2. 檢查系統中的COM口
console.log('\n步驟 2: 檢查系統COM口...');
try {
  const command = 'powershell "Get-WmiObject -Class Win32_SerialPort | Select-Object DeviceID, Name, Description, Status | ConvertTo-Json"';
  const result = execSync(command, { encoding: 'utf8' });
  console.log('系統COM口信息:');
  console.log(result);
} catch (error) {
  console.log('❌ 無法獲取系統COM口信息:', error.message);
}

// 3. 檢查COM3的詳細狀態
console.log('\n步驟 3: 檢查COM3詳細狀態...');
try {
  const com3Command = 'powershell "Get-WmiObject -Class Win32_SerialPort | Where-Object {$_.DeviceID -eq \'COM3\'} | Select-Object * | ConvertTo-Json"';
  const com3Result = execSync(com3Command, { encoding: 'utf8' });
  console.log('COM3詳細信息:');
  console.log(com3Result);
} catch (error) {
  console.log('❌ 無法獲取COM3詳細信息:', error.message);
}

// 4. 檢查COM3是否被占用
console.log('\n步驟 4: 檢查COM3是否被占用...');
try {
  const processCommand = 'powershell "Get-Process | Where-Object {$_.ProcessName -like \'*serial*\' -or $_.ProcessName -like \'*com*\' -or $_.ProcessName -like \'*printer*\'} | Select-Object ProcessName, Id | ConvertTo-Json"';
  const processResult = execSync(processCommand, { encoding: 'utf8' });
  console.log('可能占用串口的進程:');
  console.log(processResult);
} catch (error) {
  console.log('⚠️ 無法檢查進程:', error.message);
}

// 5. 嘗試使用SerialPort列出可用端口
if (serialportAvailable) {
  console.log('\n步驟 5: 使用SerialPort檢測可用端口...');
  try {
    const { SerialPort } = require('serialport');
    SerialPort.list().then(ports => {
      console.log('SerialPort檢測到的端口:');
      ports.forEach(port => {
        console.log(`  端口: ${port.path}`);
        console.log(`  製造商: ${port.manufacturer || '未知'}`);
        console.log(`  產品ID: ${port.productId || '未知'}`);
        console.log(`  供應商ID: ${port.vendorId || '未知'}`);
        console.log('  ---');
      });
      
      // 檢查COM3是否在列表中
      const com3Port = ports.find(port => port.path === 'COM3');
      if (com3Port) {
        console.log('✅ COM3在SerialPort列表中找到');
        testCOM3Connection();
      } else {
        console.log('❌ COM3不在SerialPort列表中');
      }
    }).catch(error => {
      console.log('❌ SerialPort.list()失敗:', error.message);
    });
  } catch (error) {
    console.log('❌ SerialPort檢測失敗:', error.message);
  }
} else {
  console.log('\n步驟 5: 跳過SerialPort檢測（庫不可用）');
}

// 6. 測試COM3連接
function testCOM3Connection() {
  console.log('\n步驟 6: 測試COM3連接...');
  
  const { SerialPort } = require('serialport');
  
  // 嘗試不同的波特率
  const baudRates = [9600, 115200, 38400, 19200, 4800];
  
  baudRates.forEach(baudRate => {
    console.log(`\n嘗試波特率: ${baudRate}`);
    
    try {
      const port = new SerialPort({
        path: 'COM3',
        baudRate: baudRate,
        dataBits: 8,
        stopBits: 1,
        parity: 'none',
        autoOpen: false
      });
      
      port.open((error) => {
        if (error) {
          console.log(`❌ 波特率 ${baudRate} 連接失敗:`, error.message);
        } else {
          console.log(`✅ 波特率 ${baudRate} 連接成功！`);
          
          // 發送測試命令
          const testCommand = '\x1B\x40'; // ESC @ (初始化打印機)
          port.write(testCommand, (writeError) => {
            if (writeError) {
              console.log(`❌ 發送測試命令失敗:`, writeError.message);
            } else {
              console.log(`✅ 測試命令發送成功`);
            }
            
            port.close();
          });
        }
      });
      
      // 設置超時
      setTimeout(() => {
        if (port.isOpen) {
          port.close();
        }
      }, 3000);
      
    } catch (error) {
      console.log(`❌ 創建串口實例失敗 (${baudRate}):`, error.message);
    }
  });
}

// 7. 檢查Windows權限
console.log('\n步驟 7: 檢查Windows權限...');
try {
  const whoamiCommand = 'whoami /groups';
  const whoamiResult = execSync(whoamiCommand, { encoding: 'utf8' });
  
  if (whoamiResult.includes('Administrators')) {
    console.log('✅ 當前用戶具有管理員權限');
  } else {
    console.log('⚠️ 當前用戶沒有管理員權限，這可能導致COM口訪問問題');
    console.log('建議：以管理員身份運行應用程序');
  }
} catch (error) {
  console.log('❌ 無法檢查用戶權限:', error.message);
}

// 8. 生成診斷報告
console.log('\n步驟 8: 生成診斷報告...');
const report = {
  timestamp: new Date().toISOString(),
  serialportAvailable: serialportAvailable,
  recommendations: []
};

if (!serialportAvailable) {
  report.recommendations.push('重新安裝SerialPort庫: npm install serialport@11.0.1');
}

report.recommendations.push('以管理員身份運行應用程序');
report.recommendations.push('檢查是否有其他程序占用COM3端口');
report.recommendations.push('嘗試重新安裝Zonerich打印機驅動程序');

fs.writeFileSync('com-port-diagnosis.json', JSON.stringify(report, null, 2));
console.log('✅ 診斷報告已保存到 com-port-diagnosis.json');

console.log('\n🎯 診斷完成！請查看上述結果並按照建議操作。');
