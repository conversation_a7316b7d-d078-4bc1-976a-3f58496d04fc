@echo off
echo ========================================
echo COM3端口修復工具
echo ========================================
echo.

echo 問題分析：
echo COM3端口被檢測到但無法打開 (Error code 1)
echo 這通常是因為端口被打印機驅動程序獨占使用
echo.

echo 步驟 1: 停止打印機服務...
net stop spooler
if errorlevel 1 (
    echo 警告: 無法停止打印機服務，可能需要管理員權限
) else (
    echo ✅ 打印機服務已停止
)

echo.
echo 步驟 2: 檢查COM3端口狀態...
powershell "Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like '*COM3*'} | Select-Object Name, Status, ConfigManagerErrorCode"

echo.
echo 步驟 3: 嘗試釋放COM3端口...
powershell "Get-WmiObject -Class Win32_SerialPort | Where-Object {$_.DeviceID -eq 'COM3'} | ForEach-Object {$_.Disable()}"

echo.
echo 步驟 4: 重新啟用COM3端口...
powershell "Get-WmiObject -Class Win32_SerialPort | Where-Object {$_.DeviceID -eq 'COM3'} | ForEach-Object {$_.Enable()}"

echo.
echo 步驟 5: 重新啟動打印機服務...
net start spooler
if errorlevel 1 (
    echo 警告: 無法啟動打印機服務
) else (
    echo ✅ 打印機服務已重新啟動
)

echo.
echo 步驟 6: 測試COM3端口是否可用...
node test-com3-simple.js

echo.
echo ========================================
echo 修復完成！
echo ========================================
echo.
echo 如果問題仍然存在，請嘗試：
echo 1. 重新啟動計算機
echo 2. 重新安裝Zonerich打印機驅動程序
echo 3. 使用設備管理器禁用/啟用COM3端口
echo.
pause
