{"name": "@serialport/parser-delimiter", "main": "./dist/index.js", "types": "./dist/index.d.ts", "version": "11.0.0", "engines": {"node": ">=12.0.0"}, "publishConfig": {"access": "public"}, "license": "MIT", "scripts": {"build": "tsc --build tsconfig-build.json"}, "repository": {"type": "git", "url": "git://github.com/serialport/node-serialport.git"}, "funding": "https://opencollective.com/serialport/donate", "devDependencies": {"typescript": "5.0.4"}, "gitHead": "6a8202cd947c87ac70c9f3c84d60fe4b5f5d70a9"}