{"name": "escpos", "version": "3.0.0-alpha.6", "description": "ESC/POS Printer driver for nodejs", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/song940/node-escpos.git"}, "keywords": ["escpos", "printer"], "author": "Lsong <<EMAIL>> (https://lsong.org)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "Ris<PERSON> Lima <<EMAIL>>", "<PERSON> <michael.kuenz<PERSON>@camptocamp.com>"], "license": "MIT", "bugs": {"url": "https://github.com/song940/node-escpos/issues"}, "homepage": "https://github.com/song940/node-escpos#readme", "dependencies": {"get-pixels": "*", "iconv-lite": "*", "mutable-buffer": "^2.0.3", "qr-image": "*"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">=8.x"}, "directories": {"doc": "docs", "example": "examples", "test": "test"}, "gitHead": "d1616e40f18cbeb8e1db332365fc0e167fba1308"}