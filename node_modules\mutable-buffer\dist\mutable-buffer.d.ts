/// <reference types="node" />
export declare class MutableBuffer {
    protected _initialSize: number;
    protected _blockSize: number;
    protected _size: number;
    protected _buffer: Buffer;
    get size(): number;
    get buffer(): Buffer;
    get nativeBuffer(): Buffer;
    constructor(size?: number, blockSize?: number);
    _ensure(size: number): void;
    capacity(): number;
    clear(): void;
    join(): Buffer;
    flush(): Buffer;
    write(data: any, encoding?: BufferEncoding): this;
    writeCString(data?: string | Buffer, encoding?: BufferEncoding): this;
    writeChar(c: string): this;
    writeUIntLE(value: number, byteLength: number): this;
    writeUIntBE(value: number, byteLength: number): this;
    writeUInt8(value: number): this;
    writeUInt16LE(value: number): this;
    writeUInt16BE(value: number): this;
    writeUInt32LE(value: number): this;
    writeUInt32BE(value: number): this;
    writeIntLE(value: number, byteLength: number): this;
    writeIntBE(value: number, byteLength: number): this;
    writeInt8(value: number): this;
    writeInt16LE(value: number): this;
    writeInt16BE(value: number): this;
    writeInt32LE(value: number): this;
    writeInt32BE(value: number): this;
    writeFloatLE(value: number): this;
    writeFloatBE(value: number): this;
    writeDoubleLE(value: number): this;
    writeDoubleBE(value: number): this;
    trim(): this;
    trimLeft(): this;
    trimRight(): this;
}
