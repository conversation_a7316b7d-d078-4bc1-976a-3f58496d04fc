{"name": "mutable-buffer", "version": "2.2.5", "description": "A mutable buffer library for node.js", "homepage": "https://github.com/taoyuan/mutable-buffer", "author": {"name": "taoyuan", "email": "<EMAIL>", "url": "https://github.com/taoyuan/mutable-buffer"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.16"}, "keywords": ["buffer", "mutable", "writable", "node", "nodejs"], "repository": {"type": "git", "url": "taoyuan/mutable-buffer"}, "scripts": {"prerelease": "cross-env CI=1 npm run test", "release": "np", "build": "tib-tsc", "build:full": "npm ci --ignore-scripts && npm run clean && npm run build", "coverage:summary": "tib-nyc report --reporter=text-summary", "coverage": "tib-nyc report --reporter=text-lcov | coveralls", "precoverage": "npm test", "lint": "npm run prettier:check && npm run eslint", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "eslint": "tib-eslint --report-unused-disable-directives --cache .", "eslint:fix": "npm run eslint -- --fix", "prettier:cli": "tib-prettier \"**/*.ts\" \"**/*.js\" \"**/*.md\"", "prettier:check": "npm run prettier:cli -- --check", "prettier:fix": "npm run prettier:cli -- --write", "format": "npm run prettier:fix", "clean": "tib-clean dist coverage .nyc_output *.tsbuildinfo", "pretest": "npm run clean && npm run format", "test": "tib-nyc npm run mocha", "test:ci": "tib-nyc npm run mocha", "posttest": "npm run lint", "premocha": "npm run build", "mocha": "tib-mocha \"dist/__tests__/**/*.js\"", "preunit": "npm run build", "unit": "tib-mocha \"dist/__tests__/unit/**/*.js\""}, "dependencies": {"tslib": "^2.0.1"}, "devDependencies": {"@tib/build": "^6.3.4", "@tib/eslint-config": "^8.2.4", "@tib/testlab": "^3.4.4", "@types/mocha": "^8.0.3", "@types/node": "^14.11.1", "coveralls": "^3.1.0", "cross-env": "^7.0.2", "iconv-lite": "^0.6.2", "mocha": "^8.1.3", "np": "^6.5.0", "source-map-support": "^0.5.19", "typescript": "^4.0.3"}, "license": "MIT", "files": ["README.md", "dist", "src", "!*/__tests__"]}