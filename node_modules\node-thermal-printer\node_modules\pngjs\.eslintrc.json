{"ecmaFeatures": {}, "rules": {"no-alert": 2, "no-array-constructor": 0, "no-bitwise": 0, "no-caller": 2, "no-catch-shadow": 2, "no-class-assign": 2, "no-cond-assign": 2, "no-console": 2, "no-const-assign": 2, "no-constant-condition": 2, "no-continue": 0, "no-control-regex": 2, "no-debugger": 2, "no-delete-var": 2, "no-div-regex": 0, "no-dupe-keys": 2, "no-dupe-args": 2, "no-duplicate-case": 2, "no-else-return": 1, "no-empty": 2, "no-empty-character-class": 2, "no-eq-null": 0, "no-eval": 2, "no-ex-assign": 2, "no-extend-native": 2, "no-extra-bind": 1, "no-extra-boolean-cast": 2, "no-extra-parens": 0, "no-extra-semi": 2, "no-fallthrough": 2, "no-floating-decimal": 1, "no-func-assign": 2, "no-implicit-coercion": 1, "no-implied-eval": 1, "no-inline-comments": 0, "no-inner-declarations": [2, "functions"], "no-invalid-regexp": 2, "no-invalid-this": 1, "no-irregular-whitespace": 2, "no-iterator": 1, "no-label-var": 1, "no-labels": 1, "no-lone-blocks": 1, "no-lonely-if": 1, "no-loop-func": 1, "no-mixed-requires": [1, false], "no-mixed-spaces-and-tabs": [2, false], "linebreak-style": [0, "unix"], "no-multi-spaces": 1, "no-multi-str": 0, "no-multiple-empty-lines": [1, {"max": 2}], "no-native-reassign": 2, "no-negated-in-lhs": 2, "no-nested-ternary": 1, "no-new": 1, "no-new-func": 1, "no-new-object": 1, "no-new-require": 1, "no-new-wrappers": 1, "no-obj-calls": 2, "no-octal": 2, "no-octal-escape": 0, "no-param-reassign": 1, "no-path-concat": 0, "no-plusplus": 0, "no-process-env": 0, "no-process-exit": 0, "no-proto": 0, "no-redeclare": 2, "no-regex-spaces": 2, "no-restricted-modules": 0, "no-return-assign": 1, "no-script-url": 0, "no-self-compare": 1, "no-sequences": 1, "no-shadow": 1, "no-shadow-restricted-names": 1, "no-spaced-func": 1, "no-sparse-arrays": 2, "no-sync": 0, "no-ternary": 0, "no-trailing-spaces": 1, "no-this-before-super": 1, "no-throw-literal": 1, "no-undef": 2, "no-undef-init": 1, "no-undefined": 0, "no-unexpected-multiline": 1, "no-underscore-dangle": 0, "no-unneeded-ternary": 0, "no-unreachable": 2, "no-unused-expressions": 1, "no-unused-vars": [2, {"vars": "all", "args": "after-used"}], "no-use-before-define": 2, "no-useless-call": 2, "no-void": 0, "no-var": 0, "no-warning-comments": [1, {"terms": ["todo", "fixme", "xxx"], "location": "start"}], "no-with": 1, "array-bracket-spacing": [1, "never"], "arrow-parens": 1, "arrow-spacing": 1, "accessor-pairs": 1, "block-scoped-var": 0, "brace-style": [1, "stroust<PERSON>"], "callback-return": [2, ["callback", "cb", "next"]], "camelcase": [2, {"properties": "always"}], "comma-dangle": [2, "never"], "comma-spacing": 2, "comma-style": 1, "complexity": [1, 10], "computed-property-spacing": [0, "never"], "consistent-return": 0, "consistent-this": [0, "that"], "constructor-super": 1, "curly": [1, "all"], "default-case": 1, "dot-location": [1, "property"], "dot-notation": [1, {"allowKeywords": true}], "eol-last": 0, "eqeqeq": 1, "func-names": 0, "func-style": [0, "declaration"], "generator-star-spacing": 0, "guard-for-in": 1, "handle-callback-err": 2, "id-length": [2, {"min": 3, "max": 25, "exceptions": ["x", "y", "i", "j", "ex", "up"]}], "indent": [1, 2, {"SwitchCase": 1}], "init-declarations": 0, "key-spacing": [1, {"beforeColon": false, "afterColon": true}], "keyword-spacing": 1, "lines-around-comment": 0, "max-depth": [1, 4], "max-len": [1, 160, 2], "max-nested-callbacks": [1, 2], "max-params": [1, 5], "max-statements": [1, 30], "new-cap": 1, "new-parens": 1, "newline-after-var": 0, "object-curly-spacing": [1, "always"], "object-shorthand": 0, "one-var": [1, {"initialized": "never"}], "operator-assignment": [0, "always"], "operator-linebreak": [1, "after"], "padded-blocks": 0, "prefer-const": 0, "prefer-spread": 0, "prefer-reflect": 0, "quote-props": 0, "quotes": [1, "single"], "radix": 0, "id-match": 0, "require-yield": 0, "semi": [1, "always"], "semi-spacing": [1, {"before": false, "after": true}], "sort-vars": 0, "space-before-blocks": [1, "always"], "space-before-function-paren": [1, "never"], "space-in-parens": [1, "never"], "space-infix-ops": 1, "space-unary-ops": [1, {"words": true, "nonwords": false}], "spaced-comment": 0, "strict": [2, "global"], "use-isnan": 2, "valid-jsdoc": 0, "valid-typeof": 2, "vars-on-top": 0, "wrap-iife": 0, "wrap-regex": 0, "yoda": [0, "never"]}, "env": {"node": true, "es6": true}}