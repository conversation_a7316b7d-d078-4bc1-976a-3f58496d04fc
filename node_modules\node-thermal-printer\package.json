{"name": "node-thermal-printer", "version": "4.4.0", "description": "Print on Epson, Star, Tranca, Drauma and Brother thermal printers with NodeJS", "main": "node-thermal-printer.js", "repository": {"type": "git", "url": "https://github.com/Klemen1337/node-thermal-printer"}, "keywords": ["thermal printer", "thermal", "printer", "epson", "star", "daruma", "tranca"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://kastelic.net/)", "license": "ISC", "bugs": {"url": "https://github.com/Klemen1337/node-thermal-printer/issues"}, "homepage": "https://github.com/Klemen1337/node-thermal-printer", "dependencies": {"iconv-lite": "0.5.0", "pngjs": "3.3.3", "unorm": "1.4.1", "write-file-queue": "0.0.1"}, "devDependencies": {"@types/node": "^10.14.2"}}