{"name": "parse-data-uri", "author": "jden <<EMAIL>>", "version": "0.2.0", "description": "parse a data uri into mime type and buffer", "keywords": ["mime", "data uri", "datauri"], "main": "index.js", "scripts": {"test": "mocha"}, "repository": "**************:jden/node-parse-data-uri.git", "license": "ISC", "readmeFilename": "README.md", "devDependencies": {"mochi": "0.1.0"}, "dependencies": {"data-uri-to-buffer": "0.0.3"}}