{"name": "thermal-printer-app", "version": "1.0.0", "description": "Electron desktop application for Zonerich AB-88H thermal printer integration", "main": "src/main/main-simple.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "test": "node test-setup.js"}, "keywords": ["electron", "thermal-printer", "zonerich"], "author": "", "license": "ISC", "dependencies": {"@serialport/parser-readline": "^11.0.0", "electron": "^22.3.27", "escpos": "^3.0.0-alpha.6", "node-thermal-printer": "^4.4.0", "serialport": "^11.0.1"}}