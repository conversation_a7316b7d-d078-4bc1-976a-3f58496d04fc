// 直接COM口打印機通信模組
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class DirectCOMPrinter {
  constructor() {
    this.comPort = 'COM3';
    this.isWindows = process.platform === 'win32';
  }

  // 方法1: 使用Windows MODE命令配置COM口並直接發送數據
  async printViaWindowsCOM(content) {
    try {
      console.log('使用Windows COM口直接打印...');
      
      // 配置COM3端口參數
      const configCommand = `mode ${this.comPort}: baud=9600 parity=n data=8 stop=1 dtr=on rts=on`;
      console.log('配置COM口:', configCommand);
      execSync(configCommand);
      
      // 創建臨時文件
      const tempFile = path.join(process.env.TEMP || '/tmp', 'thermal_print_data.bin');
      
      // 生成ESC/POS命令
      const escposData = this.generateESCPOSCommands(content);
      
      // 寫入二進制數據
      fs.writeFileSync(tempFile, Buffer.from(escposData, 'binary'));
      
      // 直接發送到COM口
      const copyCommand = `copy /B "${tempFile}" ${this.comPort}:`;
      console.log('發送數據到COM口:', copyCommand);
      execSync(copyCommand);
      
      // 清理臨時文件
      setTimeout(() => {
        try { fs.unlinkSync(tempFile); } catch (e) {}
      }, 2000);
      
      return {
        success: true,
        method: 'Windows COM直接通信',
        message: '數據已發送到COM3端口'
      };
    } catch (error) {
      console.error('Windows COM打印錯誤:', error);
      return {
        success: false,
        method: 'Windows COM直接通信',
        error: error.message
      };
    }
  }

  // 方法2: 使用PowerShell System.IO.Ports.SerialPort
  async printViaPowerShellSerial(content) {
    try {
      console.log('使用PowerShell SerialPort打印...');
      
      const escposData = this.generateESCPOSCommands(content);
      
      // 將ESC/POS命令轉換為PowerShell可處理的格式
      const byteArray = Array.from(Buffer.from(escposData, 'binary'))
        .map(b => b.toString())
        .join(',');
      
      const psScript = `
        Add-Type -AssemblyName System.IO.Ports
        $port = New-Object System.IO.Ports.SerialPort
        $port.PortName = "COM3"
        $port.BaudRate = 9600
        $port.DataBits = 8
        $port.StopBits = [System.IO.Ports.StopBits]::One
        $port.Parity = [System.IO.Ports.Parity]::None
        $port.DtrEnable = $true
        $port.RtsEnable = $true
        
        try {
          $port.Open()
          $bytes = @(${byteArray})
          $port.Write($bytes, 0, $bytes.Length)
          Start-Sleep -Milliseconds 500
          $port.Close()
          Write-Output "SUCCESS: Data sent to COM3"
        } catch {
          Write-Output "ERROR: $($_.Exception.Message)"
        } finally {
          if ($port.IsOpen) { $port.Close() }
        }
      `;
      
      const tempPsFile = path.join(process.env.TEMP || '/tmp', 'print_script.ps1');
      fs.writeFileSync(tempPsFile, psScript);
      
      const result = execSync(`powershell -ExecutionPolicy Bypass -File "${tempPsFile}"`, { encoding: 'utf8' });
      
      // 清理
      setTimeout(() => {
        try { fs.unlinkSync(tempPsFile); } catch (e) {}
      }, 2000);
      
      if (result.includes('SUCCESS')) {
        return {
          success: true,
          method: 'PowerShell SerialPort',
          message: '數據已通過PowerShell發送到COM3'
        };
      } else {
        throw new Error(result);
      }
    } catch (error) {
      console.error('PowerShell Serial打印錯誤:', error);
      return {
        success: false,
        method: 'PowerShell SerialPort',
        error: error.message
      };
    }
  }

  // 方法3: 使用Node.js child_process echo重定向
  async printViaEchoRedirect(content) {
    try {
      console.log('使用Echo重定向打印...');
      
      const escposData = this.generateESCPOSCommands(content);
      const tempFile = path.join(process.env.TEMP || '/tmp', 'thermal_echo_data.txt');
      
      // 寫入數據
      fs.writeFileSync(tempFile, escposData, 'binary');
      
      // 使用type命令發送到COM口
      const typeCommand = `type "${tempFile}" > ${this.comPort}:`;
      console.log('執行命令:', typeCommand);
      execSync(typeCommand);
      
      // 清理
      setTimeout(() => {
        try { fs.unlinkSync(tempFile); } catch (e) {}
      }, 2000);
      
      return {
        success: true,
        method: 'Echo重定向',
        message: '數據已通過重定向發送到COM3'
      };
    } catch (error) {
      console.error('Echo重定向打印錯誤:', error);
      return {
        success: false,
        method: 'Echo重定向',
        error: error.message
      };
    }
  }

  // 生成ESC/POS命令
  generateESCPOSCommands(content) {
    const ESC = '\x1B';
    const GS = '\x1D';
    const LF = '\x0A';
    const CR = '\x0D';
    
    let commands = '';
    
    // 初始化打印機
    commands += ESC + '@';
    
    // 設置字符集為中文
    commands += ESC + 't' + '\x01'; // 設置字符集
    
    // 標題 - 居中，雙倍大小
    commands += ESC + 'a' + '\x01'; // 居中對齊
    commands += GS + '!' + '\x11'; // 雙倍寬高
    commands += '熱敏打印機測試' + LF;
    commands += GS + '!' + '\x00'; // 恢復正常大小
    commands += 'Zonerich AB-88H 直接COM口通信' + LF;
    commands += ESC + 'a' + '\x00'; // 左對齊
    
    // 分隔線
    commands += '================================' + LF;
    
    // 系統信息
    commands += '打印機: Zonerich AB-88H' + LF;
    commands += '端口: COM3' + LF;
    commands += '時間: ' + new Date().toLocaleString('zh-TW') + LF;
    commands += '方法: 直接COM口通信' + LF + LF;
    
    // 測試消息
    commands += ESC + 'a' + '\x01'; // 居中
    commands += ESC + 'E' + '\x01'; // 粗體
    commands += '測試消息' + LF;
    commands += ESC + 'E' + '\x00'; // 取消粗體
    commands += (content.message || '直接COM口打印成功！') + LF + LF;
    commands += ESC + 'a' + '\x00'; // 左對齊
    
    // 格式化示例
    commands += ESC + 'E' + '\x01'; // 粗體
    commands += '格式化示例:' + LF;
    commands += ESC + 'E' + '\x00'; // 取消粗體
    commands += '================================' + LF;
    
    // 粗體文字
    commands += ESC + 'E' + '\x01';
    commands += '1. 粗體文字' + LF;
    commands += ESC + 'E' + '\x00';
    
    // 下劃線文字
    commands += ESC + '-' + '\x01';
    commands += '2. 下劃線文字' + LF;
    commands += ESC + '-' + '\x00';
    
    // 反色文字
    commands += GS + 'B' + '\x01';
    commands += '3. 反色文字' + LF;
    commands += GS + 'B' + '\x00';
    
    // 不同大小
    commands += GS + '!' + '\x00';
    commands += '4. 正常大小' + LF;
    commands += GS + '!' + '\x10';
    commands += '5. 雙倍高度' + LF;
    commands += GS + '!' + '\x20';
    commands += '6. 雙倍寬度' + LF;
    commands += GS + '!' + '\x00';
    
    // 對齊示例
    commands += LF + '對齊示例:' + LF;
    commands += ESC + 'a' + '\x00'; // 左對齊
    commands += '左對齊文字' + LF;
    commands += ESC + 'a' + '\x01'; // 居中
    commands += '居中對齊文字' + LF;
    commands += ESC + 'a' + '\x02'; // 右對齊
    commands += '右對齊文字' + LF;
    commands += ESC + 'a' + '\x00'; // 恢復左對齊
    
    // 收據示例
    commands += LF + '收據示例:' + LF;
    commands += '================================' + LF;
    commands += '商品1                    $10.00' + LF;
    commands += '商品2                     $5.50' + LF;
    commands += '商品3                    $15.25' + LF;
    commands += '================================' + LF;
    commands += ESC + 'E' + '\x01'; // 粗體
    commands += '總計:                    $30.75' + LF;
    commands += ESC + 'E' + '\x00'; // 取消粗體
    commands += '================================' + LF + LF;
    
    // 結尾
    commands += ESC + 'a' + '\x01'; // 居中
    commands += '謝謝測試！' + LF;
    commands += '直接COM口通信成功' + LF + LF;
    commands += '================================' + LF;
    commands += '測試完成' + LF;
    
    // 切紙
    commands += LF + LF + LF;
    commands += GS + 'V' + '\x42' + '\x00'; // 部分切紙
    
    return commands;
  }

  // 主要的測試打印方法
  async testPrint(data = {}) {
    console.log('開始直接COM口打印測試...');
    
    const results = [];
    let successCount = 0;
    
    // 嘗試方法1: Windows COM直接通信
    try {
      const result1 = await this.printViaWindowsCOM(data);
      results.push(result1);
      if (result1.success) successCount++;
    } catch (error) {
      results.push({
        success: false,
        method: 'Windows COM直接通信',
        error: error.message
      });
    }
    
    // 如果方法1失敗，嘗試方法2
    if (successCount === 0) {
      try {
        const result2 = await this.printViaPowerShellSerial(data);
        results.push(result2);
        if (result2.success) successCount++;
      } catch (error) {
        results.push({
          success: false,
          method: 'PowerShell SerialPort',
          error: error.message
        });
      }
    }
    
    // 如果前兩個方法都失敗，嘗試方法3
    if (successCount === 0) {
      try {
        const result3 = await this.printViaEchoRedirect(data);
        results.push(result3);
        if (result3.success) successCount++;
      } catch (error) {
        results.push({
          success: false,
          method: 'Echo重定向',
          error: error.message
        });
      }
    }
    
    const totalMethods = results.length;
    
    if (successCount === 0) {
      return {
        success: false,
        error: '所有直接COM口通信方法都失敗了',
        details: {
          totalMethods: totalMethods,
          successCount: successCount,
          results: results,
          failedMethods: results.map(r => r.method)
        }
      };
    }
    
    return {
      success: true,
      message: `直接COM口打印完成！成功使用了 ${successCount}/${totalMethods} 種方法`,
      timestamp: new Date().toLocaleString('zh-TW'),
      details: {
        totalMethods: totalMethods,
        successCount: successCount,
        results: results,
        successfulMethods: results.filter(r => r.success).map(r => r.method),
        failedMethods: results.filter(r => !r.success).map(r => r.method)
      }
    };
  }
}

module.exports = DirectCOMPrinter;
