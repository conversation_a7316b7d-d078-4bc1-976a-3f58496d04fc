const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { execSync } = require('child_process');
const fs = require('fs');
const os = require('os');

// Keep a global reference of the window object
let mainWindow;
let selectedPrinter = null;
const configPath = path.join(os.homedir(), '.thermal-printer-app-config.json');

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    show: false
  });

  // Load the app
  mainWindow.loadFile(path.join(__dirname, '../renderer/index-simple.html'));

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // Emitted when the window is closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Load saved configuration
  loadConfig();
}

// Load saved configuration
function loadConfig() {
  try {
    if (fs.existsSync(configPath)) {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      selectedPrinter = config.selectedPrinter;
    }
  } catch (error) {
    console.error('Error loading config:', error);
  }
}

// Save configuration
function saveConfig() {
  try {
    const config = {
      selectedPrinter: selectedPrinter,
      lastUpdated: new Date().toISOString()
    };
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  } catch (error) {
    console.error('Error saving config:', error);
  }
}

// Get available printers (Windows specific implementation)
async function getAvailablePrinters() {
  try {
    // Get Windows printers using PowerShell
    const command = 'powershell "Get-Printer | Select-Object Name, DriverName, PortName, PrinterStatus | ConvertTo-Json"';
    const result = execSync(command, { encoding: 'utf8' });
    
    let printers = JSON.parse(result);
    if (!Array.isArray(printers)) {
      printers = [printers];
    }

    // Filter and format printer list
    const formattedPrinters = printers.map(printer => ({
      name: printer.Name,
      driver: printer.DriverName,
      port: printer.PortName,
      status: mapPrinterStatus(printer.PrinterStatus),
      isZonerich: printer.Name.toLowerCase().includes('zonerich') || 
                 printer.Name.toLowerCase().includes('ab-88h'),
      isSelected: printer.Name === selectedPrinter
    }));

    return {
      success: true,
      printers: formattedPrinters,
      selectedPrinter: selectedPrinter
    };
  } catch (error) {
    console.error('Error getting printers:', error);
    return {
      success: false,
      error: error.message,
      printers: []
    };
  }
}

// Map Windows printer status to readable format
function mapPrinterStatus(status) {
  const statusMap = {
    0: 'Ready',
    1: 'Other',
    2: 'Unknown',
    3: 'Idle',
    4: 'Printing',
    5: 'Warmup',
    6: 'Stopped Printing',
    7: 'Offline'
  };
  return statusMap[status] || 'Unknown';
}

// Select a printer
async function selectPrinter(printerName) {
  try {
    selectedPrinter = printerName;
    saveConfig();
    
    return {
      success: true,
      message: `Printer "${printerName}" selected successfully`,
      selectedPrinter: printerName
    };
  } catch (error) {
    console.error('Error selecting printer:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Test print functionality (simplified version using Windows print command)
async function testPrint(data = {}) {
  try {
    if (!selectedPrinter) {
      throw new Error('No printer selected. Please select a printer first.');
    }

    const timestamp = new Date().toLocaleString();
    const testMessage = data.message || 'Test Print Successful!';

    // Create a simple text file for printing
    const printContent = `
THERMAL PRINTER TEST
Zonerich AB-88H Integration
============================

Printer: ${selectedPrinter}
Date/Time: ${timestamp}
Status: Connected & Ready
Application: Thermal Printer App v1.0

TEST MESSAGE
${testMessage}

FORMATTING EXAMPLES:
============================
1. Bold Text Example
2. Underlined Text Example  
3. Inverted Text Example
4. Small Text Size
5. Medium Text Size
6. Large Text Size

ALIGNMENT EXAMPLES:
Left Aligned Text
    Center Aligned Text
                Right Aligned Text

RECEIPT EXAMPLE:
============================
Item 1                    $10.00
Item 2                     $5.50
Item 3                    $15.25
============================
TOTAL:                    $30.75
============================

Thank you for testing!
Printer test completed successfully

============================
END OF TEST PRINT
`;

    // Write content to temporary file
    const tempFile = path.join(os.tmpdir(), 'thermal-test-print.txt');
    fs.writeFileSync(tempFile, printContent);

    // Print using Windows print command
    const printCommand = `print /D:"${selectedPrinter}" "${tempFile}"`;
    execSync(printCommand);

    // Clean up temporary file
    setTimeout(() => {
      try {
        fs.unlinkSync(tempFile);
      } catch (e) {
        console.log('Could not delete temp file:', e.message);
      }
    }, 5000);

    return {
      success: true,
      message: 'Test print completed successfully',
      timestamp: timestamp,
      details: {
        printer: selectedPrinter,
        message: testMessage,
        method: 'Windows print command'
      }
    };
  } catch (error) {
    console.error('Error during test print:', error);
    return {
      success: false,
      error: error.message,
      details: {
        printer: selectedPrinter,
        timestamp: new Date().toLocaleString(),
        errorType: error.name || 'PrintError'
      }
    };
  }
}

// Get current printer status
async function getPrinterStatus() {
  try {
    if (!selectedPrinter) {
      return {
        success: true,
        status: 'No printer selected',
        selectedPrinter: null,
        connected: false
      };
    }

    // Check if printer is still available
    const printersResult = await getAvailablePrinters();
    if (!printersResult.success) {
      throw new Error('Failed to check printer availability');
    }

    const selectedPrinterInfo = printersResult.printers.find(
      p => p.name === selectedPrinter
    );

    return {
      success: true,
      status: selectedPrinterInfo ? selectedPrinterInfo.status : 'Disconnected',
      selectedPrinter: selectedPrinter,
      connected: !!selectedPrinterInfo,
      printerInfo: selectedPrinterInfo
    };
  } catch (error) {
    console.error('Error getting printer status:', error);
    return {
      success: false,
      error: error.message,
      connected: false
    };
  }
}

// IPC handlers
ipcMain.handle('get-printers', getAvailablePrinters);
ipcMain.handle('select-printer', (event, printerName) => selectPrinter(printerName));
ipcMain.handle('test-print', (event, data) => testPrint(data));
ipcMain.handle('get-printer-status', getPrinterStatus);

// This method will be called when Electron has finished initialization
app.whenReady().then(createWindow);

// Quit when all windows are closed
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

console.log('Thermal Printer App started successfully!');
