const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { execSync } = require('child_process');
const fs = require('fs');
const os = require('os');

// Keep a global reference of the window object
let mainWindow;
let selectedPrinter = null;
const configPath = path.join(os.homedir(), '.thermal-printer-app-config.json');

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    show: false
  });

  // Load the app
  mainWindow.loadFile(path.join(__dirname, '../renderer/index-simple.html'));

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // Emitted when the window is closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Load saved configuration
  loadConfig();
}

// Load saved configuration
function loadConfig() {
  try {
    if (fs.existsSync(configPath)) {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      selectedPrinter = config.selectedPrinter;
    }
  } catch (error) {
    console.error('Error loading config:', error);
  }
}

// Save configuration
function saveConfig() {
  try {
    const config = {
      selectedPrinter: selectedPrinter,
      lastUpdated: new Date().toISOString()
    };
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  } catch (error) {
    console.error('Error saving config:', error);
  }
}

// Get available printers (Windows specific implementation)
async function getAvailablePrinters() {
  try {
    console.log('Getting available printers...');

    // Get Windows printers using PowerShell with proper JSON formatting
    const command = 'powershell "Get-Printer -ErrorAction SilentlyContinue | Select-Object Name, DriverName, PortName, PrinterStatus | ConvertTo-Json"';
    const result = execSync(command, { encoding: 'utf8' });

    console.log('Raw printer list output:', result);

    let printers = [];

    if (result && result.trim()) {
      try {
        const parsed = JSON.parse(result.trim());
        printers = Array.isArray(parsed) ? parsed : [parsed];
      } catch (jsonError) {
        console.log('JSON parse failed for printer list, using fallback method...');

        // Fallback: Parse the output manually
        const lines = result.split('\n').filter(line => line.trim());
        let currentPrinter = {};

        for (const line of lines) {
          const trimmedLine = line.trim();
          if (trimmedLine.includes('Name') && trimmedLine.includes(':')) {
            if (currentPrinter.Name) {
              printers.push(currentPrinter);
            }
            currentPrinter = {
              Name: trimmedLine.split(':')[1]?.trim() || 'Unknown',
              DriverName: 'Unknown',
              PortName: 'Unknown',
              PrinterStatus: 'Unknown'
            };
          } else if (trimmedLine.includes('DriverName') && currentPrinter.Name) {
            currentPrinter.DriverName = trimmedLine.split(':')[1]?.trim() || 'Unknown';
          } else if (trimmedLine.includes('PortName') && currentPrinter.Name) {
            currentPrinter.PortName = trimmedLine.split(':')[1]?.trim() || 'Unknown';
          } else if (trimmedLine.includes('PrinterStatus') && currentPrinter.Name) {
            currentPrinter.PrinterStatus = trimmedLine.split(':')[1]?.trim() || 'Unknown';
          }
        }

        if (currentPrinter.Name) {
          printers.push(currentPrinter);
        }

        // If still no printers found, try simple method
        if (printers.length === 0) {
          try {
            const simpleCommand = 'powershell "Get-Printer | Select-Object -ExpandProperty Name"';
            const simpleResult = execSync(simpleCommand, { encoding: 'utf8' });
            const printerNames = simpleResult.split('\n').filter(name => name.trim());

            printers = printerNames.map(name => ({
              Name: name.trim(),
              DriverName: 'Unknown',
              PortName: 'Unknown',
              PrinterStatus: 'Unknown'
            }));
          } catch (e) {
            console.log('Simple method also failed:', e.message);
          }
        }
      }
    }

    // Filter and format printer list
    const formattedPrinters = printers.map(printer => ({
      name: printer.Name || 'Unknown',
      driver: printer.DriverName || 'Unknown',
      port: printer.PortName || 'Unknown',
      status: mapPrinterStatus(printer.PrinterStatus),
      isZonerich: (printer.Name || '').toLowerCase().includes('zonerich') ||
                 (printer.Name || '').toLowerCase().includes('ab-88h'),
      isSelected: printer.Name === selectedPrinter
    }));

    console.log(`Found ${formattedPrinters.length} printers:`, formattedPrinters);

    return {
      success: true,
      printers: formattedPrinters,
      selectedPrinter: selectedPrinter
    };
  } catch (error) {
    console.error('Error getting printers:', error);
    return {
      success: false,
      error: error.message,
      printers: []
    };
  }
}

// Map Windows printer status to readable format
function mapPrinterStatus(status) {
  const statusMap = {
    0: 'Ready',
    1: 'Other',
    2: 'Unknown',
    3: 'Idle',
    4: 'Printing',
    5: 'Warmup',
    6: 'Stopped Printing',
    7: 'Offline'
  };
  return statusMap[status] || 'Unknown';
}

// Select a printer
async function selectPrinter(printerName) {
  try {
    selectedPrinter = printerName;
    saveConfig();
    
    return {
      success: true,
      message: `Printer "${printerName}" selected successfully`,
      selectedPrinter: printerName
    };
  } catch (error) {
    console.error('Error selecting printer:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Test basic printer connectivity
async function testPrinterConnectivity(printerName) {
  try {
    console.log(`Testing connectivity for printer: ${printerName}`);

    // Test 1: Check if printer exists in Windows (using ConvertTo-Json for proper JSON output)
    const command = `powershell "Get-Printer -Name '${printerName}' -ErrorAction SilentlyContinue | Select-Object Name, PrinterStatus, JobCount | ConvertTo-Json"`;
    const result = execSync(command, { encoding: 'utf8' });

    console.log('Raw PowerShell output:', result);

    let printerInfo = null;
    if (result && result.trim()) {
      try {
        printerInfo = JSON.parse(result.trim());
      } catch (jsonError) {
        console.log('JSON parse failed, trying alternative method...');
        // Alternative: Parse manually if JSON fails
        const lines = result.split('\n');
        printerInfo = {
          Name: printerName,
          PrinterStatus: 'Unknown',
          JobCount: 0
        };

        // Try to extract status from output
        for (const line of lines) {
          if (line.includes('Normal') || line.includes('Ready')) {
            printerInfo.PrinterStatus = 'Normal';
          } else if (line.includes('Offline')) {
            printerInfo.PrinterStatus = 'Offline';
          }
        }
      }
    }

    // Test 2: Check print queue (simplified)
    let queueInfo = 'No jobs in queue';
    try {
      const queueCommand = `powershell "Get-PrintJob -PrinterName '${printerName}' -ErrorAction SilentlyContinue | Measure-Object | Select-Object -ExpandProperty Count"`;
      const queueResult = execSync(queueCommand, { encoding: 'utf8' });
      const jobCount = parseInt(queueResult.trim()) || 0;
      queueInfo = `${jobCount} jobs in queue`;
    } catch (e) {
      console.log('Queue check failed:', e.message);
      queueInfo = 'Queue check failed';
    }

    // Test 3: Simple printer availability test
    let isAvailable = false;
    try {
      const availabilityCommand = `powershell "Get-Printer -Name '${printerName}' -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Name"`;
      const availabilityResult = execSync(availabilityCommand, { encoding: 'utf8' });
      isAvailable = availabilityResult.trim().includes(printerName);
    } catch (e) {
      console.log('Availability check failed:', e.message);
    }

    return {
      success: true,
      printerExists: !!printerInfo && isAvailable,
      printerStatus: printerInfo?.PrinterStatus || 'Unknown',
      jobCount: printerInfo?.JobCount || 0,
      queueInfo: queueInfo,
      isAvailable: isAvailable,
      rawOutput: result
    };
  } catch (error) {
    console.error('Connectivity test error:', error);
    return {
      success: false,
      error: error.message,
      printerExists: false,
      isAvailable: false
    };
  }
}

// Enhanced test print with multiple methods and better error detection
async function testPrint(data = {}) {
  try {
    if (!selectedPrinter) {
      throw new Error('No printer selected. Please select a printer first.');
    }

    const timestamp = new Date().toLocaleString();
    const testMessage = data.message || 'Test Print Successful!';

    // Step 1: Test printer connectivity first
    console.log('Testing printer connectivity...');
    const connectivityTest = await testPrinterConnectivity(selectedPrinter);
    if (!connectivityTest.success) {
      throw new Error(`Printer connectivity test failed: ${connectivityTest.error}`);
    }

    console.log('Printer connectivity test passed:', connectivityTest);

    // Step 2: Try multiple printing methods
    const results = [];

    // Method 1: Windows print command (for compatibility)
    try {
      const method1Result = await printWithWindowsCommand(selectedPrinter, testMessage, timestamp);
      results.push(method1Result);
    } catch (error) {
      results.push({ method: 'Windows print command', success: false, error: error.message });
    }

    // Method 2: PowerShell Out-Printer (more reliable)
    try {
      const method2Result = await printWithPowerShell(selectedPrinter, testMessage, timestamp);
      results.push(method2Result);
    } catch (error) {
      results.push({ method: 'PowerShell Out-Printer', success: false, error: error.message });
    }

    // Method 3: Direct port communication (for thermal printers)
    try {
      const method3Result = await printWithDirectPort(selectedPrinter, testMessage, timestamp);
      results.push(method3Result);
    } catch (error) {
      results.push({ method: 'Direct port communication', success: false, error: error.message });
    }

    // Check if any method succeeded
    const successfulMethods = results.filter(r => r.success);
    const failedMethods = results.filter(r => !r.success);

    if (successfulMethods.length === 0) {
      throw new Error(`All printing methods failed: ${failedMethods.map(f => f.error).join('; ')}`);
    }

    // Step 3: Verify print job was actually sent
    setTimeout(async () => {
      try {
        const postPrintTest = await testPrinterConnectivity(selectedPrinter);
        console.log('Post-print connectivity test:', postPrintTest);
      } catch (e) {
        console.log('Post-print test failed:', e.message);
      }
    }, 2000);

    return {
      success: true,
      message: `Test print completed using ${successfulMethods.length} method(s)`,
      timestamp: timestamp,
      details: {
        printer: selectedPrinter,
        message: testMessage,
        connectivityTest: connectivityTest,
        methods: results,
        successfulMethods: successfulMethods.map(m => m.method),
        failedMethods: failedMethods.map(m => m.method)
      }
    };
  } catch (error) {
    console.error('Error during test print:', error);
    return {
      success: false,
      error: error.message,
      details: {
        printer: selectedPrinter,
        timestamp: new Date().toLocaleString(),
        errorType: error.name || 'PrintError'
      }
    };
  }
}

// Method 1: Windows print command
async function printWithWindowsCommand(printerName, testMessage, timestamp) {
  const printContent = createPrintContent(testMessage, timestamp, 'Windows Print Command');
  const tempFile = path.join(os.tmpdir(), 'thermal-test-print.txt');

  fs.writeFileSync(tempFile, printContent);

  const printCommand = `print /D:"${printerName}" "${tempFile}"`;
  const result = execSync(printCommand, { encoding: 'utf8' });

  // Clean up
  setTimeout(() => {
    try { fs.unlinkSync(tempFile); } catch (e) { }
  }, 5000);

  return {
    method: 'Windows print command',
    success: true,
    output: result,
    file: tempFile
  };
}

// Method 2: PowerShell Out-Printer
async function printWithPowerShell(printerName, testMessage, timestamp) {
  const printContent = createPrintContent(testMessage, timestamp, 'PowerShell Out-Printer');
  const tempFile = path.join(os.tmpdir(), 'thermal-test-powershell.txt');

  fs.writeFileSync(tempFile, printContent);

  const psCommand = `powershell "Get-Content '${tempFile}' | Out-Printer -Name '${printerName}'"`;
  const result = execSync(psCommand, { encoding: 'utf8' });

  // Clean up
  setTimeout(() => {
    try { fs.unlinkSync(tempFile); } catch (e) { }
  }, 5000);

  return {
    method: 'PowerShell Out-Printer',
    success: true,
    output: result,
    file: tempFile
  };
}

// Method 3: Direct port communication with ESC/POS commands
async function printWithDirectPort(printerName, testMessage, timestamp) {
  try {
    // Get printer port information
    const portCommand = `powershell "Get-Printer -Name '${printerName}' | Select-Object PortName"`;
    const portResult = execSync(portCommand, { encoding: 'utf8' });
    const portInfo = JSON.parse(portResult);
    const portName = portInfo.PortName;

    // Create ESC/POS formatted content for thermal printer
    const escPosContent = createESCPOSContent(testMessage, timestamp);
    const tempFile = path.join(os.tmpdir(), 'thermal-test-escpos.bin');

    // Write binary ESC/POS data
    fs.writeFileSync(tempFile, Buffer.from(escPosContent, 'binary'));

    // Try to send directly to printer port
    let result;
    if (portName.startsWith('USB') || portName.startsWith('COM')) {
      // For USB or COM ports, try copy command
      const copyCommand = `copy /B "${tempFile}" "${portName}"`;
      result = execSync(copyCommand, { encoding: 'utf8' });
    } else {
      // For network printers, fall back to print command
      const printCommand = `print /D:"${printerName}" "${tempFile}"`;
      result = execSync(printCommand, { encoding: 'utf8' });
    }

    // Clean up
    setTimeout(() => {
      try { fs.unlinkSync(tempFile); } catch (e) { }
    }, 5000);

    return {
      method: 'Direct port communication',
      success: true,
      output: result,
      port: portName,
      file: tempFile
    };
  } catch (error) {
    return {
      method: 'Direct port communication',
      success: false,
      error: error.message
    };
  }
}

// Create standard text content for printing
function createPrintContent(testMessage, timestamp, method) {
  return `
THERMAL PRINTER TEST
Zonerich AB-88H Integration
Method: ${method}
============================

Printer: ${selectedPrinter}
Date/Time: ${timestamp}
Status: Connected & Ready
Application: Thermal Printer App v1.0

TEST MESSAGE
${testMessage}

FORMATTING EXAMPLES:
============================
1. Bold Text Example
2. Underlined Text Example
3. Inverted Text Example
4. Small Text Size
5. Medium Text Size
6. Large Text Size

ALIGNMENT EXAMPLES:
Left Aligned Text
    Center Aligned Text
                Right Aligned Text

RECEIPT EXAMPLE:
============================
Item 1                    $10.00
Item 2                     $5.50
Item 3                    $15.25
============================
TOTAL:                    $30.75
============================

Thank you for testing!
Printer test completed successfully
Method: ${method}

============================
END OF TEST PRINT
`;
}

// Create ESC/POS formatted content for thermal printers
function createESCPOSContent(testMessage, timestamp) {
  const ESC = '\x1B';
  const GS = '\x1D';

  let content = '';

  // Initialize printer
  content += ESC + '@'; // Initialize

  // Header - Center aligned, double size
  content += ESC + 'a' + '\x01'; // Center align
  content += GS + '!' + '\x11'; // Double width and height
  content += 'THERMAL PRINTER TEST\n';
  content += GS + '!' + '\x00'; // Normal size
  content += 'Zonerich AB-88H Integration\n';
  content += ESC + 'a' + '\x00'; // Left align

  // Line
  content += '================================\n';

  // Printer info
  content += `Printer: ${selectedPrinter}\n`;
  content += `Date/Time: ${timestamp}\n`;
  content += 'Status: Connected & Ready\n';
  content += 'Application: Thermal Printer App v1.0\n\n';

  // Test message - Center aligned, emphasized
  content += ESC + 'a' + '\x01'; // Center align
  content += ESC + 'E' + '\x01'; // Bold on
  content += 'TEST MESSAGE\n';
  content += ESC + 'E' + '\x00'; // Bold off
  content += testMessage + '\n\n';
  content += ESC + 'a' + '\x00'; // Left align

  // Formatting examples
  content += 'FORMATTING EXAMPLES:\n';
  content += '================================\n';

  // Bold
  content += ESC + 'E' + '\x01'; // Bold on
  content += '1. Bold Text Example\n';
  content += ESC + 'E' + '\x00'; // Bold off

  // Underline
  content += ESC + '-' + '\x01'; // Underline on
  content += '2. Underlined Text Example\n';
  content += ESC + '-' + '\x00'; // Underline off

  // Inverted
  content += GS + 'B' + '\x01'; // White on black
  content += '3. Inverted Text Example\n';
  content += GS + 'B' + '\x00'; // Normal

  // Different sizes
  content += GS + '!' + '\x00'; // Normal
  content += '4. Normal Text Size\n';
  content += GS + '!' + '\x10'; // Double height
  content += '5. Double Height\n';
  content += GS + '!' + '\x20'; // Double width
  content += '6. Double Width\n';
  content += GS + '!' + '\x00'; // Back to normal

  // Alignment examples
  content += '\nALIGNMENT EXAMPLES:\n';
  content += ESC + 'a' + '\x00'; // Left
  content += 'Left Aligned Text\n';
  content += ESC + 'a' + '\x01'; // Center
  content += 'Center Aligned Text\n';
  content += ESC + 'a' + '\x02'; // Right
  content += 'Right Aligned Text\n';
  content += ESC + 'a' + '\x00'; // Back to left

  // Receipt example
  content += '\nRECEIPT EXAMPLE:\n';
  content += '================================\n';
  content += 'Item 1                    $10.00\n';
  content += 'Item 2                     $5.50\n';
  content += 'Item 3                    $15.25\n';
  content += '================================\n';
  content += ESC + 'E' + '\x01'; // Bold
  content += 'TOTAL:                    $30.75\n';
  content += ESC + 'E' + '\x00'; // Normal
  content += '================================\n\n';

  // Footer
  content += ESC + 'a' + '\x01'; // Center
  content += 'Thank you for testing!\n';
  content += 'Printer test completed successfully\n\n';
  content += '================================\n';
  content += 'END OF TEST PRINT\n';

  // Cut paper
  content += '\n\n\n';
  content += GS + 'V' + '\x42' + '\x00'; // Partial cut

  return content;
}

// Get current printer status
async function getPrinterStatus() {
  try {
    if (!selectedPrinter) {
      return {
        success: true,
        status: 'No printer selected',
        selectedPrinter: null,
        connected: false
      };
    }

    // Check if printer is still available
    const printersResult = await getAvailablePrinters();
    if (!printersResult.success) {
      throw new Error('Failed to check printer availability');
    }

    const selectedPrinterInfo = printersResult.printers.find(
      p => p.name === selectedPrinter
    );

    return {
      success: true,
      status: selectedPrinterInfo ? selectedPrinterInfo.status : 'Disconnected',
      selectedPrinter: selectedPrinter,
      connected: !!selectedPrinterInfo,
      printerInfo: selectedPrinterInfo
    };
  } catch (error) {
    console.error('Error getting printer status:', error);
    return {
      success: false,
      error: error.message,
      connected: false
    };
  }
}

// 導入增強版熱敏打印機管理器
let thermalPrinterManager = null;
try {
  thermalPrinterManager = require('./thermal-printer-manager');
  console.log('增強版熱敏打印機管理器載入成功');
} catch (error) {
  console.log('增強版熱敏打印機管理器載入失敗，使用基本版本:', error.message);
}

// 導入直接COM口打印機
let directCOMPrinter = null;
try {
  const DirectCOMPrinter = require('./direct-com-printer');
  directCOMPrinter = new DirectCOMPrinter();
  console.log('直接COM口打印機載入成功');
} catch (error) {
  console.log('直接COM口打印機載入失敗:', error.message);
}

// IPC handlers
ipcMain.handle('get-printers', getAvailablePrinters);
ipcMain.handle('select-printer', async (event, printerName) => {
  // 首先使用基本版本選擇打印機
  const basicResult = await selectPrinter(printerName);

  // 如果有增強版管理器，也嘗試使用它
  if (thermalPrinterManager && basicResult.success) {
    try {
      const enhancedResult = await thermalPrinterManager.selectPrinter(printerName);
      return {
        ...basicResult,
        enhanced: enhancedResult,
        hasDirectConnection: enhancedResult.hasDirectConnection || false,
        availableMethods: enhancedResult.availableMethods || []
      };
    } catch (error) {
      console.log('增強版選擇打印機失敗:', error.message);
      return basicResult;
    }
  }

  return basicResult;
});

ipcMain.handle('test-print', async (event, data) => {
  console.log('開始測試打印，嘗試多種方法...');

  // 優先級1: 直接COM口打印（最可靠）
  if (directCOMPrinter && selectedPrinter === 'Zonerich AB-88H') {
    try {
      console.log('嘗試直接COM口打印...');
      const directResult = await directCOMPrinter.testPrint(data);

      if (directResult.success) {
        console.log('✅ 直接COM口打印成功');
        return directResult;
      } else {
        console.log('❌ 直接COM口打印失敗:', directResult.error);
      }
    } catch (error) {
      console.log('❌ 直接COM口打印錯誤:', error.message);
    }
  }

  // 優先級2: 增強版熱敏打印機管理器
  if (thermalPrinterManager && selectedPrinter) {
    try {
      console.log('嘗試增強版熱敏打印機管理器...');
      const enhancedResult = await thermalPrinterManager.testPrint(data);

      if (enhancedResult.success) {
        console.log('✅ 增強版打印成功');
        return enhancedResult;
      } else {
        console.log('❌ 增強版打印失敗:', enhancedResult.error);
      }
    } catch (error) {
      console.log('❌ 增強版打印錯誤:', error.message);
    }
  }

  // 優先級3: 基本版本（PowerShell備用）
  console.log('使用基本版本PowerShell方法...');
  return await testPrint(data);
});

ipcMain.handle('get-printer-status', getPrinterStatus);
ipcMain.handle('test-printer-connectivity', (event, printerName) => testPrinterConnectivity(printerName || selectedPrinter));

// 新增：獲取可用串口
ipcMain.handle('get-serial-ports', async () => {
  if (thermalPrinterManager) {
    try {
      const ports = await thermalPrinterManager.getAvailableSerialPorts();
      return {
        success: true,
        ports: ports
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        ports: []
      };
    }
  } else {
    return {
      success: false,
      error: '增強版熱敏打印機管理器未可用',
      ports: []
    };
  }
});

// 新增：直接COM口打印
ipcMain.handle('direct-com-print', async (event, data) => {
  if (directCOMPrinter) {
    try {
      console.log('執行直接COM口打印...');
      const result = await directCOMPrinter.testPrint(data);
      return result;
    } catch (error) {
      console.error('直接COM口打印錯誤:', error);
      return {
        success: false,
        error: error.message,
        method: '直接COM口打印'
      };
    }
  } else {
    return {
      success: false,
      error: '直接COM口打印機未可用',
      method: '直接COM口打印'
    };
  }
});

// This method will be called when Electron has finished initialization
app.whenReady().then(createWindow);

// Quit when all windows are closed
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

console.log('Thermal Printer App started successfully!');
