const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { execSync } = require('child_process');
const fs = require('fs');
const os = require('os');

// Keep a global reference of the window object
let mainWindow;
let selectedPrinter = null;
const configPath = path.join(os.homedir(), '.thermal-printer-app-config.json');

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    show: false
  });

  // Load the app
  mainWindow.loadFile(path.join(__dirname, '../renderer/index-simple.html'));

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // Emitted when the window is closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Load saved configuration
  loadConfig();
}

// Load saved configuration
function loadConfig() {
  try {
    if (fs.existsSync(configPath)) {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      selectedPrinter = config.selectedPrinter;
    }
  } catch (error) {
    console.error('Error loading config:', error);
  }
}

// Save configuration
function saveConfig() {
  try {
    const config = {
      selectedPrinter: selectedPrinter,
      lastUpdated: new Date().toISOString()
    };
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  } catch (error) {
    console.error('Error saving config:', error);
  }
}

// Get available printers (Windows specific implementation)
async function getAvailablePrinters() {
  try {
    // Get Windows printers using PowerShell
    const command = 'powershell "Get-Printer | Select-Object Name, DriverName, PortName, PrinterStatus | ConvertTo-Json"';
    const result = execSync(command, { encoding: 'utf8' });
    
    let printers = JSON.parse(result);
    if (!Array.isArray(printers)) {
      printers = [printers];
    }

    // Filter and format printer list
    const formattedPrinters = printers.map(printer => ({
      name: printer.Name,
      driver: printer.DriverName,
      port: printer.PortName,
      status: mapPrinterStatus(printer.PrinterStatus),
      isZonerich: printer.Name.toLowerCase().includes('zonerich') || 
                 printer.Name.toLowerCase().includes('ab-88h'),
      isSelected: printer.Name === selectedPrinter
    }));

    return {
      success: true,
      printers: formattedPrinters,
      selectedPrinter: selectedPrinter
    };
  } catch (error) {
    console.error('Error getting printers:', error);
    return {
      success: false,
      error: error.message,
      printers: []
    };
  }
}

// Map Windows printer status to readable format
function mapPrinterStatus(status) {
  const statusMap = {
    0: 'Ready',
    1: 'Other',
    2: 'Unknown',
    3: 'Idle',
    4: 'Printing',
    5: 'Warmup',
    6: 'Stopped Printing',
    7: 'Offline'
  };
  return statusMap[status] || 'Unknown';
}

// Select a printer
async function selectPrinter(printerName) {
  try {
    selectedPrinter = printerName;
    saveConfig();
    
    return {
      success: true,
      message: `Printer "${printerName}" selected successfully`,
      selectedPrinter: printerName
    };
  } catch (error) {
    console.error('Error selecting printer:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Test basic printer connectivity
async function testPrinterConnectivity(printerName) {
  try {
    // Test 1: Check if printer exists in Windows
    const command = `powershell "Get-Printer -Name '${printerName}' | Select-Object Name, PrinterStatus, JobCount"`;
    const result = execSync(command, { encoding: 'utf8' });
    const printerInfo = JSON.parse(result);

    // Test 2: Check print queue
    const queueCommand = `powershell "Get-PrintJob -PrinterName '${printerName}'"`;
    let queueInfo = '';
    try {
      queueInfo = execSync(queueCommand, { encoding: 'utf8' });
    } catch (e) {
      queueInfo = 'No jobs in queue';
    }

    return {
      success: true,
      printerExists: !!printerInfo,
      printerStatus: printerInfo?.PrinterStatus || 'Unknown',
      jobCount: printerInfo?.JobCount || 0,
      queueInfo: queueInfo
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// Enhanced test print with multiple methods and better error detection
async function testPrint(data = {}) {
  try {
    if (!selectedPrinter) {
      throw new Error('No printer selected. Please select a printer first.');
    }

    const timestamp = new Date().toLocaleString();
    const testMessage = data.message || 'Test Print Successful!';

    // Step 1: Test printer connectivity first
    console.log('Testing printer connectivity...');
    const connectivityTest = await testPrinterConnectivity(selectedPrinter);
    if (!connectivityTest.success) {
      throw new Error(`Printer connectivity test failed: ${connectivityTest.error}`);
    }

    console.log('Printer connectivity test passed:', connectivityTest);

    // Step 2: Try multiple printing methods
    const results = [];

    // Method 1: Windows print command (for compatibility)
    try {
      const method1Result = await printWithWindowsCommand(selectedPrinter, testMessage, timestamp);
      results.push(method1Result);
    } catch (error) {
      results.push({ method: 'Windows print command', success: false, error: error.message });
    }

    // Method 2: PowerShell Out-Printer (more reliable)
    try {
      const method2Result = await printWithPowerShell(selectedPrinter, testMessage, timestamp);
      results.push(method2Result);
    } catch (error) {
      results.push({ method: 'PowerShell Out-Printer', success: false, error: error.message });
    }

    // Method 3: Direct port communication (for thermal printers)
    try {
      const method3Result = await printWithDirectPort(selectedPrinter, testMessage, timestamp);
      results.push(method3Result);
    } catch (error) {
      results.push({ method: 'Direct port communication', success: false, error: error.message });
    }

    // Check if any method succeeded
    const successfulMethods = results.filter(r => r.success);
    const failedMethods = results.filter(r => !r.success);

    if (successfulMethods.length === 0) {
      throw new Error(`All printing methods failed: ${failedMethods.map(f => f.error).join('; ')}`);
    }

    // Step 3: Verify print job was actually sent
    setTimeout(async () => {
      try {
        const postPrintTest = await testPrinterConnectivity(selectedPrinter);
        console.log('Post-print connectivity test:', postPrintTest);
      } catch (e) {
        console.log('Post-print test failed:', e.message);
      }
    }, 2000);

    return {
      success: true,
      message: `Test print completed using ${successfulMethods.length} method(s)`,
      timestamp: timestamp,
      details: {
        printer: selectedPrinter,
        message: testMessage,
        connectivityTest: connectivityTest,
        methods: results,
        successfulMethods: successfulMethods.map(m => m.method),
        failedMethods: failedMethods.map(m => m.method)
      }
    };
  } catch (error) {
    console.error('Error during test print:', error);
    return {
      success: false,
      error: error.message,
      details: {
        printer: selectedPrinter,
        timestamp: new Date().toLocaleString(),
        errorType: error.name || 'PrintError'
      }
    };
  }
}

// Method 1: Windows print command
async function printWithWindowsCommand(printerName, testMessage, timestamp) {
  const printContent = createPrintContent(testMessage, timestamp, 'Windows Print Command');
  const tempFile = path.join(os.tmpdir(), 'thermal-test-print.txt');

  fs.writeFileSync(tempFile, printContent);

  const printCommand = `print /D:"${printerName}" "${tempFile}"`;
  const result = execSync(printCommand, { encoding: 'utf8' });

  // Clean up
  setTimeout(() => {
    try { fs.unlinkSync(tempFile); } catch (e) { }
  }, 5000);

  return {
    method: 'Windows print command',
    success: true,
    output: result,
    file: tempFile
  };
}

// Method 2: PowerShell Out-Printer
async function printWithPowerShell(printerName, testMessage, timestamp) {
  const printContent = createPrintContent(testMessage, timestamp, 'PowerShell Out-Printer');
  const tempFile = path.join(os.tmpdir(), 'thermal-test-powershell.txt');

  fs.writeFileSync(tempFile, printContent);

  const psCommand = `powershell "Get-Content '${tempFile}' | Out-Printer -Name '${printerName}'"`;
  const result = execSync(psCommand, { encoding: 'utf8' });

  // Clean up
  setTimeout(() => {
    try { fs.unlinkSync(tempFile); } catch (e) { }
  }, 5000);

  return {
    method: 'PowerShell Out-Printer',
    success: true,
    output: result,
    file: tempFile
  };
}

// Method 3: Direct port communication with ESC/POS commands
async function printWithDirectPort(printerName, testMessage, timestamp) {
  try {
    // Get printer port information
    const portCommand = `powershell "Get-Printer -Name '${printerName}' | Select-Object PortName"`;
    const portResult = execSync(portCommand, { encoding: 'utf8' });
    const portInfo = JSON.parse(portResult);
    const portName = portInfo.PortName;

    // Create ESC/POS formatted content for thermal printer
    const escPosContent = createESCPOSContent(testMessage, timestamp);
    const tempFile = path.join(os.tmpdir(), 'thermal-test-escpos.bin');

    // Write binary ESC/POS data
    fs.writeFileSync(tempFile, Buffer.from(escPosContent, 'binary'));

    // Try to send directly to printer port
    let result;
    if (portName.startsWith('USB') || portName.startsWith('COM')) {
      // For USB or COM ports, try copy command
      const copyCommand = `copy /B "${tempFile}" "${portName}"`;
      result = execSync(copyCommand, { encoding: 'utf8' });
    } else {
      // For network printers, fall back to print command
      const printCommand = `print /D:"${printerName}" "${tempFile}"`;
      result = execSync(printCommand, { encoding: 'utf8' });
    }

    // Clean up
    setTimeout(() => {
      try { fs.unlinkSync(tempFile); } catch (e) { }
    }, 5000);

    return {
      method: 'Direct port communication',
      success: true,
      output: result,
      port: portName,
      file: tempFile
    };
  } catch (error) {
    return {
      method: 'Direct port communication',
      success: false,
      error: error.message
    };
  }
}

// Create standard text content for printing
function createPrintContent(testMessage, timestamp, method) {
  return `
THERMAL PRINTER TEST
Zonerich AB-88H Integration
Method: ${method}
============================

Printer: ${selectedPrinter}
Date/Time: ${timestamp}
Status: Connected & Ready
Application: Thermal Printer App v1.0

TEST MESSAGE
${testMessage}

FORMATTING EXAMPLES:
============================
1. Bold Text Example
2. Underlined Text Example
3. Inverted Text Example
4. Small Text Size
5. Medium Text Size
6. Large Text Size

ALIGNMENT EXAMPLES:
Left Aligned Text
    Center Aligned Text
                Right Aligned Text

RECEIPT EXAMPLE:
============================
Item 1                    $10.00
Item 2                     $5.50
Item 3                    $15.25
============================
TOTAL:                    $30.75
============================

Thank you for testing!
Printer test completed successfully
Method: ${method}

============================
END OF TEST PRINT
`;
}

// Create ESC/POS formatted content for thermal printers
function createESCPOSContent(testMessage, timestamp) {
  const ESC = '\x1B';
  const GS = '\x1D';

  let content = '';

  // Initialize printer
  content += ESC + '@'; // Initialize

  // Header - Center aligned, double size
  content += ESC + 'a' + '\x01'; // Center align
  content += GS + '!' + '\x11'; // Double width and height
  content += 'THERMAL PRINTER TEST\n';
  content += GS + '!' + '\x00'; // Normal size
  content += 'Zonerich AB-88H Integration\n';
  content += ESC + 'a' + '\x00'; // Left align

  // Line
  content += '================================\n';

  // Printer info
  content += `Printer: ${selectedPrinter}\n`;
  content += `Date/Time: ${timestamp}\n`;
  content += 'Status: Connected & Ready\n';
  content += 'Application: Thermal Printer App v1.0\n\n';

  // Test message - Center aligned, emphasized
  content += ESC + 'a' + '\x01'; // Center align
  content += ESC + 'E' + '\x01'; // Bold on
  content += 'TEST MESSAGE\n';
  content += ESC + 'E' + '\x00'; // Bold off
  content += testMessage + '\n\n';
  content += ESC + 'a' + '\x00'; // Left align

  // Formatting examples
  content += 'FORMATTING EXAMPLES:\n';
  content += '================================\n';

  // Bold
  content += ESC + 'E' + '\x01'; // Bold on
  content += '1. Bold Text Example\n';
  content += ESC + 'E' + '\x00'; // Bold off

  // Underline
  content += ESC + '-' + '\x01'; // Underline on
  content += '2. Underlined Text Example\n';
  content += ESC + '-' + '\x00'; // Underline off

  // Inverted
  content += GS + 'B' + '\x01'; // White on black
  content += '3. Inverted Text Example\n';
  content += GS + 'B' + '\x00'; // Normal

  // Different sizes
  content += GS + '!' + '\x00'; // Normal
  content += '4. Normal Text Size\n';
  content += GS + '!' + '\x10'; // Double height
  content += '5. Double Height\n';
  content += GS + '!' + '\x20'; // Double width
  content += '6. Double Width\n';
  content += GS + '!' + '\x00'; // Back to normal

  // Alignment examples
  content += '\nALIGNMENT EXAMPLES:\n';
  content += ESC + 'a' + '\x00'; // Left
  content += 'Left Aligned Text\n';
  content += ESC + 'a' + '\x01'; // Center
  content += 'Center Aligned Text\n';
  content += ESC + 'a' + '\x02'; // Right
  content += 'Right Aligned Text\n';
  content += ESC + 'a' + '\x00'; // Back to left

  // Receipt example
  content += '\nRECEIPT EXAMPLE:\n';
  content += '================================\n';
  content += 'Item 1                    $10.00\n';
  content += 'Item 2                     $5.50\n';
  content += 'Item 3                    $15.25\n';
  content += '================================\n';
  content += ESC + 'E' + '\x01'; // Bold
  content += 'TOTAL:                    $30.75\n';
  content += ESC + 'E' + '\x00'; // Normal
  content += '================================\n\n';

  // Footer
  content += ESC + 'a' + '\x01'; // Center
  content += 'Thank you for testing!\n';
  content += 'Printer test completed successfully\n\n';
  content += '================================\n';
  content += 'END OF TEST PRINT\n';

  // Cut paper
  content += '\n\n\n';
  content += GS + 'V' + '\x42' + '\x00'; // Partial cut

  return content;
}

// Get current printer status
async function getPrinterStatus() {
  try {
    if (!selectedPrinter) {
      return {
        success: true,
        status: 'No printer selected',
        selectedPrinter: null,
        connected: false
      };
    }

    // Check if printer is still available
    const printersResult = await getAvailablePrinters();
    if (!printersResult.success) {
      throw new Error('Failed to check printer availability');
    }

    const selectedPrinterInfo = printersResult.printers.find(
      p => p.name === selectedPrinter
    );

    return {
      success: true,
      status: selectedPrinterInfo ? selectedPrinterInfo.status : 'Disconnected',
      selectedPrinter: selectedPrinter,
      connected: !!selectedPrinterInfo,
      printerInfo: selectedPrinterInfo
    };
  } catch (error) {
    console.error('Error getting printer status:', error);
    return {
      success: false,
      error: error.message,
      connected: false
    };
  }
}

// IPC handlers
ipcMain.handle('get-printers', getAvailablePrinters);
ipcMain.handle('select-printer', (event, printerName) => selectPrinter(printerName));
ipcMain.handle('test-print', (event, data) => testPrint(data));
ipcMain.handle('get-printer-status', getPrinterStatus);
ipcMain.handle('test-printer-connectivity', (event, printerName) => testPrinterConnectivity(printerName || selectedPrinter));

// This method will be called when Electron has finished initialization
app.whenReady().then(createWindow);

// Quit when all windows are closed
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

console.log('Thermal Printer App started successfully!');
