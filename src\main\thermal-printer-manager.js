// 使用try-catch來處理可能的依賴問題
let ThermalPrinter, PrinterTypes, CharacterSet, BreakLine;
let <PERSON><PERSON>Port, ReadlineParser;
let escpos;

try {
  const thermalPrinterLib = require('node-thermal-printer');
  ThermalPrinter = thermalPrinterLib.ThermalPrinter;
  PrinterTypes = thermalPrinterLib.PrinterTypes;
  CharacterSet = thermalPrinterLib.CharacterSet;
  BreakLine = thermalPrinterLib.BreakLine;
} catch (error) {
  console.log('node-thermal-printer not available:', error.message);
}

try {
  const serialportLib = require('serialport');
  SerialPort = serialportLib.SerialPort;
  const parserLib = require('@serialport/parser-readline');
  ReadlineParser = parserLib.ReadlineParser;
} catch (error) {
  console.log('serialport not available:', error.message);
}

try {
  escpos = require('escpos');
} catch (error) {
  console.log('escpos not available:', error.message);
}
const fs = require('fs');
const path = require('path');
const os = require('os');
const execSync = require('child_process').execSync;

class ThermalPrinterManager {
  constructor() {
    this.selectedPrinter = null;
    this.printerPort = null;
    this.thermalPrinter = null;
    this.serialPort = null;
    this.escPosDevice = null;
    this.configPath = path.join(os.homedir(), '.thermal-printer-app-config.json');
    this.loadConfig();
  }

  // 載入配置
  loadConfig() {
    try {
      if (fs.existsSync(this.configPath)) {
        const config = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
        this.selectedPrinter = config.selectedPrinter;
        this.printerPort = config.printerPort;
      }
    } catch (error) {
      console.error('載入配置錯誤:', error);
    }
  }

  // 保存配置
  saveConfig() {
    try {
      const config = {
        selectedPrinter: this.selectedPrinter,
        printerPort: this.printerPort,
        lastUpdated: new Date().toISOString()
      };
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
    } catch (error) {
      console.error('保存配置錯誤:', error);
    }
  }

  // 獲取可用的串口列表
  async getAvailableSerialPorts() {
    try {
      const ports = await SerialPort.list();
      return ports.filter(port => 
        port.path.startsWith('COM') || 
        port.path.startsWith('/dev/tty') ||
        port.path.startsWith('/dev/cu.')
      );
    } catch (error) {
      console.error('獲取串口列表錯誤:', error);
      return [];
    }
  }

  // 檢測Zonerich打印機端口
  async detectZonerichPort() {
    try {
      const ports = await this.getAvailableSerialPorts();
      console.log('可用串口:', ports);

      // 優先檢查COM3（從之前的測試中知道Zonerich在COM3）
      const com3Port = ports.find(port => port.path === 'COM3');
      if (com3Port) {
        console.log('找到COM3端口，可能是Zonerich打印機');
        return 'COM3';
      }

      // 檢查其他可能的端口
      for (const port of ports) {
        if (port.manufacturer && 
            (port.manufacturer.toLowerCase().includes('zonerich') ||
             port.manufacturer.toLowerCase().includes('prolific'))) {
          console.log(`找到可能的Zonerich端口: ${port.path}`);
          return port.path;
        }
      }

      return null;
    } catch (error) {
      console.error('檢測Zonerich端口錯誤:', error);
      return null;
    }
  }

  // 初始化node-thermal-printer
  async initNodeThermalPrinter(printerName, portPath) {
    try {
      console.log(`初始化node-thermal-printer: ${printerName} on ${portPath}`);
      
      let interface;
      if (portPath && portPath.startsWith('COM')) {
        // 串口連接
        interface = `serial:${portPath}`;
      } else {
        // Windows打印機名稱
        interface = `printer:${printerName}`;
      }

      this.thermalPrinter = new ThermalPrinter({
        type: PrinterTypes.EPSON,  // Zonerich AB-88H兼容ESC/POS
        interface: interface,
        characterSet: CharacterSet.PC852_LATIN2,
        removeSpecialCharacters: false,
        lineCharacter: "=",
        breakLine: BreakLine.WORD,
        options: {
          timeout: 5000,
          baudRate: 9600,
          dataBits: 8,
          stopBits: 1,
          parity: 'none'
        }
      });

      // 測試連接
      const isConnected = await this.thermalPrinter.isPrinterConnected();
      console.log(`node-thermal-printer連接狀態: ${isConnected}`);
      
      return isConnected;
    } catch (error) {
      console.error('初始化node-thermal-printer錯誤:', error);
      return false;
    }
  }

  // 初始化SerialPort直接通信
  async initSerialPort(portPath) {
    try {
      console.log(`初始化SerialPort: ${portPath}`);
      
      this.serialPort = new SerialPort({
        path: portPath,
        baudRate: 9600,
        dataBits: 8,
        stopBits: 1,
        parity: 'none'
      });

      const parser = this.serialPort.pipe(new ReadlineParser({ delimiter: '\r\n' }));
      
      return new Promise((resolve, reject) => {
        this.serialPort.on('open', () => {
          console.log('SerialPort連接成功');
          resolve(true);
        });

        this.serialPort.on('error', (error) => {
          console.error('SerialPort連接錯誤:', error);
          reject(error);
        });

        // 設置超時
        setTimeout(() => {
          if (!this.serialPort.isOpen) {
            reject(new Error('SerialPort連接超時'));
          }
        }, 5000);
      });
    } catch (error) {
      console.error('初始化SerialPort錯誤:', error);
      return false;
    }
  }

  // 初始化ESC/POS
  async initESCPOS(portPath) {
    try {
      console.log(`初始化ESC/POS: ${portPath}`);
      
      // 創建串口設備
      const device = new escpos.Serial(portPath, {
        baudRate: 9600,
        dataBits: 8,
        stopBits: 1,
        parity: 'none'
      });

      this.escPosDevice = new escpos.Printer(device);
      
      return new Promise((resolve, reject) => {
        device.open((error) => {
          if (error) {
            console.error('ESC/POS設備打開錯誤:', error);
            reject(error);
          } else {
            console.log('ESC/POS設備連接成功');
            resolve(true);
          }
        });
      });
    } catch (error) {
      console.error('初始化ESC/POS錯誤:', error);
      return false;
    }
  }

  // 選擇打印機並初始化所有連接方式
  async selectPrinter(printerName) {
    try {
      console.log(`選擇打印機: ${printerName}`);
      this.selectedPrinter = printerName;

      // 檢測端口
      let portPath = null;
      if (printerName === 'Zonerich AB-88H') {
        portPath = await this.detectZonerichPort();
        if (portPath) {
          this.printerPort = portPath;
          console.log(`檢測到Zonerich端口: ${portPath}`);
        }
      }

      // 保存配置
      this.saveConfig();

      // 初始化結果
      const results = {
        printerName: printerName,
        portPath: portPath,
        methods: {}
      };

      // 嘗試初始化各種連接方式
      try {
        results.methods.nodeThermalPrinter = await this.initNodeThermalPrinter(printerName, portPath);
      } catch (error) {
        console.log('node-thermal-printer初始化失敗:', error.message);
        results.methods.nodeThermalPrinter = false;
      }

      if (portPath) {
        try {
          results.methods.serialPort = await this.initSerialPort(portPath);
        } catch (error) {
          console.log('SerialPort初始化失敗:', error.message);
          results.methods.serialPort = false;
        }

        try {
          results.methods.escpos = await this.initESCPOS(portPath);
        } catch (error) {
          console.log('ESC/POS初始化失敗:', error.message);
          results.methods.escpos = false;
        }
      }

      const successfulMethods = Object.keys(results.methods).filter(
        method => results.methods[method]
      );

      return {
        success: true,
        message: `打印機 "${printerName}" 選擇成功`,
        details: results,
        availableMethods: successfulMethods,
        hasDirectConnection: successfulMethods.length > 0
      };
    } catch (error) {
      console.error('選擇打印機錯誤:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 使用node-thermal-printer進行打印
  async printWithNodeThermalPrinter(testMessage, timestamp) {
    try {
      if (!this.thermalPrinter) {
        throw new Error('node-thermal-printer未初始化');
      }

      console.log('使用node-thermal-printer打印...');

      // 清除之前的內容
      this.thermalPrinter.clear();

      // 打印標題
      this.thermalPrinter.alignCenter();
      this.thermalPrinter.setTextSize(1, 1);
      this.thermalPrinter.bold(true);
      this.thermalPrinter.println('熱敏打印機測試');
      this.thermalPrinter.bold(false);
      this.thermalPrinter.setTextNormal();
      this.thermalPrinter.println('Zonerich AB-88H 集成測試');
      this.thermalPrinter.drawLine();

      // 打印系統信息
      this.thermalPrinter.alignLeft();
      this.thermalPrinter.println(`打印機: ${this.selectedPrinter}`);
      this.thermalPrinter.println(`端口: ${this.printerPort || 'Windows打印機'}`);
      this.thermalPrinter.println(`時間: ${timestamp}`);
      this.thermalPrinter.println('方法: node-thermal-printer');
      this.thermalPrinter.newLine();

      // 打印測試消息
      this.thermalPrinter.alignCenter();
      this.thermalPrinter.bold(true);
      this.thermalPrinter.println('測試消息');
      this.thermalPrinter.bold(false);
      this.thermalPrinter.println(testMessage);
      this.thermalPrinter.newLine();

      // 格式化示例
      this.thermalPrinter.alignLeft();
      this.thermalPrinter.bold(true);
      this.thermalPrinter.println('格式化示例:');
      this.thermalPrinter.bold(false);
      this.thermalPrinter.drawLine();

      // 粗體
      this.thermalPrinter.bold(true);
      this.thermalPrinter.println('1. 粗體文字');
      this.thermalPrinter.bold(false);

      // 下劃線
      this.thermalPrinter.underline(true);
      this.thermalPrinter.println('2. 下劃線文字');
      this.thermalPrinter.underline(false);

      // 反色
      this.thermalPrinter.invert(true);
      this.thermalPrinter.println('3. 反色文字');
      this.thermalPrinter.invert(false);

      // 不同大小
      this.thermalPrinter.setTextSize(0, 0);
      this.thermalPrinter.println('4. 小字體');
      this.thermalPrinter.setTextSize(1, 0);
      this.thermalPrinter.println('5. 中等字體');
      this.thermalPrinter.setTextSize(1, 1);
      this.thermalPrinter.println('6. 大字體');
      this.thermalPrinter.setTextNormal();

      // 對齊示例
      this.thermalPrinter.newLine();
      this.thermalPrinter.println('對齊示例:');
      this.thermalPrinter.alignLeft();
      this.thermalPrinter.println('左對齊文字');
      this.thermalPrinter.alignCenter();
      this.thermalPrinter.println('居中對齊文字');
      this.thermalPrinter.alignRight();
      this.thermalPrinter.println('右對齊文字');
      this.thermalPrinter.alignLeft();

      // 收據示例
      this.thermalPrinter.newLine();
      this.thermalPrinter.drawLine();
      this.thermalPrinter.println('收據示例:');
      this.thermalPrinter.drawLine();
      this.thermalPrinter.println('商品1                    $10.00');
      this.thermalPrinter.println('商品2                     $5.50');
      this.thermalPrinter.println('商品3                    $15.25');
      this.thermalPrinter.drawLine();
      this.thermalPrinter.bold(true);
      this.thermalPrinter.println('總計:                    $30.75');
      this.thermalPrinter.bold(false);
      this.thermalPrinter.drawLine();

      // 結尾
      this.thermalPrinter.newLine();
      this.thermalPrinter.alignCenter();
      this.thermalPrinter.println('謝謝測試！');
      this.thermalPrinter.println('node-thermal-printer 打印成功');
      this.thermalPrinter.newLine();
      this.thermalPrinter.drawLine();
      this.thermalPrinter.println('測試完成');
      this.thermalPrinter.newLine();
      this.thermalPrinter.newLine();
      this.thermalPrinter.cut();

      // 執行打印
      await this.thermalPrinter.execute();

      return {
        success: true,
        method: 'node-thermal-printer',
        message: '使用node-thermal-printer打印成功'
      };
    } catch (error) {
      console.error('node-thermal-printer打印錯誤:', error);
      return {
        success: false,
        method: 'node-thermal-printer',
        error: error.message
      };
    }
  }

  // 使用SerialPort直接發送ESC/POS命令
  async printWithSerialPort(testMessage, timestamp) {
    try {
      if (!this.serialPort || !this.serialPort.isOpen) {
        throw new Error('SerialPort未連接');
      }

      console.log('使用SerialPort直接打印...');

      // ESC/POS命令
      const ESC = '\x1B';
      const GS = '\x1D';

      let commands = '';

      // 初始化打印機
      commands += ESC + '@';

      // 標題 - 居中，雙倍大小
      commands += ESC + 'a' + '\x01'; // 居中
      commands += GS + '!' + '\x11'; // 雙倍寬高
      commands += '熱敏打印機測試\n';
      commands += GS + '!' + '\x00'; // 正常大小
      commands += 'Zonerich AB-88H SerialPort測試\n';
      commands += ESC + 'a' + '\x00'; // 左對齊

      // 分隔線
      commands += '================================\n';

      // 系統信息
      commands += `打印機: ${this.selectedPrinter}\n`;
      commands += `端口: ${this.printerPort}\n`;
      commands += `時間: ${timestamp}\n`;
      commands += '方法: SerialPort直接通信\n\n';

      // 測試消息 - 居中，粗體
      commands += ESC + 'a' + '\x01'; // 居中
      commands += ESC + 'E' + '\x01'; // 粗體開
      commands += '測試消息\n';
      commands += ESC + 'E' + '\x00'; // 粗體關
      commands += testMessage + '\n\n';
      commands += ESC + 'a' + '\x00'; // 左對齊

      // 格式化示例
      commands += '格式化示例:\n';
      commands += '================================\n';

      // 粗體
      commands += ESC + 'E' + '\x01';
      commands += '1. 粗體文字\n';
      commands += ESC + 'E' + '\x00';

      // 下劃線
      commands += ESC + '-' + '\x01';
      commands += '2. 下劃線文字\n';
      commands += ESC + '-' + '\x00';

      // 反色
      commands += GS + 'B' + '\x01';
      commands += '3. 反色文字\n';
      commands += GS + 'B' + '\x00';

      // 不同大小
      commands += GS + '!' + '\x00';
      commands += '4. 正常大小\n';
      commands += GS + '!' + '\x10';
      commands += '5. 雙倍高度\n';
      commands += GS + '!' + '\x20';
      commands += '6. 雙倍寬度\n';
      commands += GS + '!' + '\x00';

      // 對齊示例
      commands += '\n對齊示例:\n';
      commands += ESC + 'a' + '\x00';
      commands += '左對齊文字\n';
      commands += ESC + 'a' + '\x01';
      commands += '居中對齊文字\n';
      commands += ESC + 'a' + '\x02';
      commands += '右對齊文字\n';
      commands += ESC + 'a' + '\x00';

      // 收據示例
      commands += '\n收據示例:\n';
      commands += '================================\n';
      commands += '商品1                    $10.00\n';
      commands += '商品2                     $5.50\n';
      commands += '商品3                    $15.25\n';
      commands += '================================\n';
      commands += ESC + 'E' + '\x01';
      commands += '總計:                    $30.75\n';
      commands += ESC + 'E' + '\x00';
      commands += '================================\n\n';

      // 結尾
      commands += ESC + 'a' + '\x01';
      commands += '謝謝測試！\n';
      commands += 'SerialPort 直接通信成功\n\n';
      commands += '================================\n';
      commands += '測試完成\n';

      // 切紙
      commands += '\n\n\n';
      commands += GS + 'V' + '\x42' + '\x00';

      // 發送命令
      return new Promise((resolve, reject) => {
        this.serialPort.write(Buffer.from(commands, 'binary'), (error) => {
          if (error) {
            reject(error);
          } else {
            console.log('SerialPort命令發送成功');
            resolve({
              success: true,
              method: 'SerialPort',
              message: '使用SerialPort直接通信打印成功'
            });
          }
        });
      });
    } catch (error) {
      console.error('SerialPort打印錯誤:', error);
      return {
        success: false,
        method: 'SerialPort',
        error: error.message
      };
    }
  }

  // 使用ESC/POS庫打印
  async printWithESCPOS(testMessage, timestamp) {
    try {
      if (!this.escPosDevice) {
        throw new Error('ESC/POS設備未初始化');
      }

      console.log('使用ESC/POS庫打印...');

      return new Promise((resolve, reject) => {
        this.escPosDevice
          .font('a')
          .align('ct')
          .style('bu')
          .size(1, 1)
          .text('熱敏打印機測試')
          .style('normal')
          .size(0, 0)
          .text('Zonerich AB-88H ESC/POS測試')
          .text('================================')
          .align('lt')
          .text(`打印機: ${this.selectedPrinter}`)
          .text(`端口: ${this.printerPort}`)
          .text(`時間: ${timestamp}`)
          .text('方法: ESC/POS庫')
          .text('')
          .align('ct')
          .style('b')
          .text('測試消息')
          .style('normal')
          .text(testMessage)
          .text('')
          .align('lt')
          .style('b')
          .text('格式化示例:')
          .style('normal')
          .text('================================')
          .style('b')
          .text('1. 粗體文字')
          .style('normal')
          .style('u')
          .text('2. 下劃線文字')
          .style('normal')
          .style('i')
          .text('3. 斜體文字')
          .style('normal')
          .size(0, 0)
          .text('4. 正常大小')
          .size(1, 0)
          .text('5. 雙倍高度')
          .size(1, 1)
          .text('6. 雙倍大小')
          .size(0, 0)
          .text('')
          .text('對齊示例:')
          .align('lt')
          .text('左對齊文字')
          .align('ct')
          .text('居中對齊文字')
          .align('rt')
          .text('右對齊文字')
          .align('lt')
          .text('')
          .text('收據示例:')
          .text('================================')
          .text('商品1                    $10.00')
          .text('商品2                     $5.50')
          .text('商品3                    $15.25')
          .text('================================')
          .style('b')
          .text('總計:                    $30.75')
          .style('normal')
          .text('================================')
          .text('')
          .align('ct')
          .text('謝謝測試！')
          .text('ESC/POS庫打印成功')
          .text('')
          .text('================================')
          .text('測試完成')
          .text('')
          .text('')
          .cut()
          .close((error) => {
            if (error) {
              reject(error);
            } else {
              resolve({
                success: true,
                method: 'ESC/POS',
                message: '使用ESC/POS庫打印成功'
              });
            }
          });
      });
    } catch (error) {
      console.error('ESC/POS打印錯誤:', error);
      return {
        success: false,
        method: 'ESC/POS',
        error: error.message
      };
    }
  }

  // 主要的測試打印功能 - 嘗試所有可用方法
  async testPrint(data = {}) {
    try {
      if (!this.selectedPrinter) {
        throw new Error('未選擇打印機。請先選擇打印機。');
      }

      const timestamp = new Date().toLocaleString('zh-TW');
      const testMessage = data.message || '熱敏打印機測試成功！';

      console.log('開始測試打印...');

      const results = [];
      let successCount = 0;

      // 方法1: node-thermal-printer
      if (this.thermalPrinter) {
        try {
          console.log('嘗試node-thermal-printer方法...');
          const result = await this.printWithNodeThermalPrinter(testMessage, timestamp);
          results.push(result);
          if (result.success) successCount++;
        } catch (error) {
          results.push({
            success: false,
            method: 'node-thermal-printer',
            error: error.message
          });
        }
      }

      // 方法2: SerialPort直接通信
      if (this.serialPort && this.serialPort.isOpen) {
        try {
          console.log('嘗試SerialPort直接通信方法...');
          const result = await this.printWithSerialPort(testMessage, timestamp);
          results.push(result);
          if (result.success) successCount++;
        } catch (error) {
          results.push({
            success: false,
            method: 'SerialPort',
            error: error.message
          });
        }
      }

      // 方法3: ESC/POS庫
      if (this.escPosDevice) {
        try {
          console.log('嘗試ESC/POS庫方法...');
          const result = await this.printWithESCPOS(testMessage, timestamp);
          results.push(result);
          if (result.success) successCount++;
        } catch (error) {
          results.push({
            success: false,
            method: 'ESC/POS',
            error: error.message
          });
        }
      }

      // 如果沒有直接連接方法可用，回退到PowerShell方法
      if (results.length === 0) {
        console.log('沒有直接連接方法可用，使用PowerShell備用方案...');
        const fallbackResult = await this.printWithPowerShellFallback(testMessage, timestamp);
        results.push(fallbackResult);
        if (fallbackResult.success) successCount++;
      }

      const totalMethods = results.length;
      const failedMethods = results.filter(r => !r.success);

      if (successCount === 0) {
        throw new Error(`所有打印方法都失敗了: ${failedMethods.map(f => f.error).join('; ')}`);
      }

      return {
        success: true,
        message: `測試打印完成！成功使用了 ${successCount}/${totalMethods} 種方法`,
        timestamp: timestamp,
        details: {
          printer: this.selectedPrinter,
          port: this.printerPort,
          message: testMessage,
          totalMethods: totalMethods,
          successCount: successCount,
          results: results,
          successfulMethods: results.filter(r => r.success).map(r => r.method),
          failedMethods: failedMethods.map(r => r.method)
        }
      };
    } catch (error) {
      console.error('測試打印錯誤:', error);
      return {
        success: false,
        error: error.message,
        details: {
          printer: this.selectedPrinter,
          timestamp: new Date().toLocaleString('zh-TW'),
          errorType: error.name || 'PrintError'
        }
      };
    }
  }

  // PowerShell備用方案
  async printWithPowerShellFallback(testMessage, timestamp) {
    try {
      console.log('使用PowerShell備用方案...');

      const printContent = `
熱敏打印機測試 (PowerShell備用)
Zonerich AB-88H 集成測試
================================

打印機: ${this.selectedPrinter}
端口: ${this.printerPort || 'Windows打印機'}
時間: ${timestamp}
方法: PowerShell Out-Printer (備用)

測試消息
${testMessage}

格式化示例:
================================
1. 粗體文字示例
2. 下劃線文字示例
3. 反色文字示例
4. 小字體示例
5. 中等字體示例
6. 大字體示例

對齊示例:
左對齊文字
    居中對齊文字
                右對齊文字

收據示例:
================================
商品1                    $10.00
商品2                     $5.50
商品3                    $15.25
================================
總計:                    $30.75
================================

謝謝測試！
PowerShell備用方案打印成功

================================
測試完成
`;

      const tempFile = path.join(os.tmpdir(), 'thermal-test-fallback.txt');
      fs.writeFileSync(tempFile, printContent);

      const psCommand = `powershell "Get-Content '${tempFile}' | Out-Printer -Name '${this.selectedPrinter}'"`;
      execSync(psCommand);

      // 清理
      setTimeout(() => {
        try { fs.unlinkSync(tempFile); } catch (e) { }
      }, 5000);

      return {
        success: true,
        method: 'PowerShell備用',
        message: '使用PowerShell備用方案打印成功'
      };
    } catch (error) {
      return {
        success: false,
        method: 'PowerShell備用',
        error: error.message
      };
    }
  }

  // 關閉所有連接
  async closeConnections() {
    try {
      if (this.serialPort && this.serialPort.isOpen) {
        this.serialPort.close();
      }

      if (this.escPosDevice) {
        this.escPosDevice.close();
      }

      // node-thermal-printer會自動管理連接

      console.log('所有連接已關閉');
    } catch (error) {
      console.error('關閉連接錯誤:', error);
    }
  }
}

// 導出單例實例
const thermalPrinterManager = new ThermalPrinterManager();

module.exports = {
  selectPrinter: (printerName) => thermalPrinterManager.selectPrinter(printerName),
  testPrint: (data) => thermalPrinterManager.testPrint(data),
  getAvailableSerialPorts: () => thermalPrinterManager.getAvailableSerialPorts(),
  closeConnections: () => thermalPrinterManager.closeConnections()
};
