const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Application state
let printers = [];
let selectedPrinter = null;
let autoRefreshInterval = null;
let isAutoRefreshing = false;

// DOM elements
const elements = {
  alert: document.getElementById('alert'),
  alertMessage: document.getElementById('alert-message'),
  refreshBtn: document.getElementById('refresh-btn'),
  refreshLoading: document.getElementById('refresh-loading'),
  refreshText: document.getElementById('refresh-text'),
  noPrinters: document.getElementById('no-printers'),
  printerList: document.getElementById('printer-list'),
  noPrinterSelected: document.getElementById('no-printer-selected'),
  printerSelected: document.getElementById('printer-selected'),
  selectedPrinterName: document.getElementById('selected-printer-name'),
  printerStatus: document.getElementById('printer-status'),
  testMessage: document.getElementById('test-message'),
  testPrintBtn: document.getElementById('test-print-btn'),
  testConnectivityBtn: document.getElementById('test-connectivity-btn'),
  getSerialPortsBtn: document.getElementById('get-serial-ports-btn'),
  directComPrintBtn: document.getElementById('direct-com-print-btn'),
  directComLoading: document.getElementById('direct-com-loading'),
  directComText: document.getElementById('direct-com-text'),
  connectionInfo: document.getElementById('connection-info'),
  connectionDetails: document.getElementById('connection-details'),
  printLoading: document.getElementById('print-loading'),
  printText: document.getElementById('print-text'),
  autoRefreshBtn: document.getElementById('auto-refresh-btn'),
  statusPrinter: document.getElementById('status-printer'),
  statusConnection: document.getElementById('status-connection'),
  statusText: document.getElementById('status-text'),
  lastUpdated: document.getElementById('last-updated')
};

// Initialize application
document.addEventListener('DOMContentLoaded', async () => {
  console.log('Thermal Printer App loaded');
  
  // Set up event listeners
  elements.refreshBtn.addEventListener('click', refreshPrinters);
  elements.testPrintBtn.addEventListener('click', performTestPrint);
  elements.testConnectivityBtn.addEventListener('click', testConnectivity);
  elements.getSerialPortsBtn.addEventListener('click', getSerialPorts);
  elements.directComPrintBtn.addEventListener('click', performDirectComPrint);
  elements.autoRefreshBtn.addEventListener('click', toggleAutoRefresh);
  
  // Initial load
  await refreshPrinters();
  await updatePrinterStatus();
  
  showAlert('success', 'Application initialized successfully');
});

// Show alert message
function showAlert(type, message) {
  elements.alert.className = `alert alert-${type}`;
  elements.alertMessage.textContent = message;
  elements.alert.classList.remove('hidden');
  
  // Auto-hide after 5 seconds for success/info, 8 seconds for errors
  const hideDelay = type === 'error' ? 8000 : 5000;
  setTimeout(() => {
    elements.alert.classList.add('hidden');
  }, hideDelay);
}

// Refresh printers list
async function refreshPrinters() {
  elements.refreshLoading.classList.remove('hidden');
  elements.refreshText.textContent = 'Refreshing...';
  elements.refreshBtn.disabled = true;
  
  try {
    const result = await ipcRenderer.invoke('get-printers');
    
    if (result.success) {
      printers = result.printers || [];
      selectedPrinter = result.selectedPrinter;
      
      renderPrinterList();
      updateSelectedPrinterUI();
      
      const printerCount = printers.length;
      const zonerichCount = printers.filter(p => p.isZonerich).length;
      
      let message = `Found ${printerCount} printer(s)`;
      if (zonerichCount > 0) {
        message += ` (${zonerichCount} Zonerich)`;
      }
      
      showAlert('success', message);
    } else {
      showAlert('error', `Failed to get printers: ${result.error}`);
    }
  } catch (error) {
    showAlert('error', `Failed to refresh printers: ${error.message}`);
  } finally {
    elements.refreshLoading.classList.add('hidden');
    elements.refreshText.textContent = 'Refresh Printers';
    elements.refreshBtn.disabled = false;
  }
}

// Render printer list
function renderPrinterList() {
  elements.printerList.innerHTML = '';
  
  if (printers.length === 0) {
    elements.noPrinters.classList.remove('hidden');
    return;
  }
  
  elements.noPrinters.classList.add('hidden');
  
  printers.forEach(printer => {
    const li = document.createElement('li');
    li.className = 'printer-item';
    
    if (printer.isSelected) {
      li.classList.add('selected');
    }
    
    if (printer.isZonerich) {
      li.classList.add('zonerich');
    }
    
    li.innerHTML = `
      <div class="printer-name">
        ${printer.name}
        ${printer.isZonerich ? '<span style="color: #28a745; font-weight: bold;">(Zonerich)</span>' : ''}
      </div>
      <div class="printer-details">
        Driver: ${printer.driver}<br>
        Port: ${printer.port}<br>
        Status: <span class="status status-${printer.status.toLowerCase().replace(' ', '-')}">${printer.status}</span>
      </div>
    `;
    
    li.addEventListener('click', () => selectPrinter(printer.name));
    elements.printerList.appendChild(li);
  });
}

// Select a printer
async function selectPrinter(printerName) {
  try {
    const result = await ipcRenderer.invoke('select-printer', printerName);

    if (result.success) {
      selectedPrinter = printerName;

      // Update the selected state in the printers list
      printers.forEach(printer => {
        printer.isSelected = printer.name === printerName;
      });

      renderPrinterList();
      updateSelectedPrinterUI();

      let message = `打印機 "${printerName}" 選擇成功`;

      // 顯示增強功能信息
      if (result.enhanced) {
        const enhanced = result.enhanced;
        if (enhanced.hasDirectConnection) {
          message += `\n✅ 直接連接可用`;
          message += `\n可用方法: ${enhanced.availableMethods.join(', ')}`;

          // 顯示連接詳情
          showConnectionInfo(enhanced.details);
        } else {
          message += `\n⚠️ 僅PowerShell方法可用`;
        }
      }

      showAlert('success', message);

      // Update printer status after selection
      await updatePrinterStatus();
    } else {
      showAlert('error', `選擇打印機失敗: ${result.error}`);
    }
  } catch (error) {
    showAlert('error', `選擇打印機失敗: ${error.message}`);
  }
}

// 顯示連接信息
function showConnectionInfo(details) {
  if (!details) return;

  let html = `
    <p><strong>打印機:</strong> ${details.printerName}</p>
    <p><strong>端口:</strong> ${details.portPath || '未檢測到'}</p>
    <h5>連接方法狀態:</h5>
    <ul>
  `;

  Object.keys(details.methods).forEach(method => {
    const status = details.methods[method];
    const icon = status ? '✅' : '❌';
    const methodNames = {
      'nodeThermalPrinter': 'node-thermal-printer',
      'serialPort': 'SerialPort直接通信',
      'escpos': 'ESC/POS庫'
    };
    html += `<li>${icon} ${methodNames[method] || method}: ${status ? '成功' : '失敗'}</li>`;
  });

  html += '</ul>';

  elements.connectionDetails.innerHTML = html;
  elements.connectionInfo.style.display = 'block';
}

// Update selected printer UI
function updateSelectedPrinterUI() {
  if (selectedPrinter) {
    elements.noPrinterSelected.classList.add('hidden');
    elements.printerSelected.classList.remove('hidden');
    elements.selectedPrinterName.textContent = selectedPrinter;
  } else {
    elements.noPrinterSelected.classList.remove('hidden');
    elements.printerSelected.classList.add('hidden');
  }
}

// Test printer connectivity
async function testConnectivity() {
  if (!selectedPrinter) {
    showAlert('error', '請先選擇打印機');
    return;
  }

  try {
    showAlert('info', '正在測試打印機連接...');
    const result = await ipcRenderer.invoke('test-printer-connectivity', selectedPrinter);

    if (result.success) {
      const details = [
        `打印機存在: ${result.printerExists ? '是' : '否'}`,
        `狀態: ${result.printerStatus}`,
        `隊列中的作業: ${result.jobCount}`,
        `隊列信息: ${result.queueInfo}`
      ].join('\n');

      showAlert('success', `連接測試通過！\n${details}`);
    } else {
      showAlert('error', `連接測試失敗: ${result.error}`);
    }
  } catch (error) {
    showAlert('error', `連接測試錯誤: ${error.message}`);
  }
}

// 獲取串口設備
async function getSerialPorts() {
  try {
    showAlert('info', '正在檢測串口設備...');
    const result = await ipcRenderer.invoke('get-serial-ports');

    if (result.success) {
      if (result.ports.length === 0) {
        showAlert('info', '未檢測到串口設備');
        return;
      }

      let message = `檢測到 ${result.ports.length} 個串口設備:\n\n`;
      result.ports.forEach(port => {
        message += `端口: ${port.path}\n`;
        if (port.manufacturer) {
          message += `製造商: ${port.manufacturer}\n`;
        }
        if (port.vendorId) {
          message += `供應商ID: ${port.vendorId}\n`;
        }
        if (port.productId) {
          message += `產品ID: ${port.productId}\n`;
        }
        message += '\n';
      });

      // 檢查是否有COM3（Zonerich端口）
      const com3Port = result.ports.find(port => port.path === 'COM3');
      if (com3Port) {
        message += '✅ 檢測到COM3端口（可能是Zonerich AB-88H）';
      }

      showAlert('success', message);

      // 在控制台顯示詳細信息
      console.log('檢測到的串口設備:', result.ports);
    } else {
      showAlert('error', `檢測串口設備失敗: ${result.error}`);
    }
  } catch (error) {
    showAlert('error', `檢測串口設備錯誤: ${error.message}`);
  }
}

// 執行直接COM口打印
async function performDirectComPrint() {
  if (!selectedPrinter) {
    showAlert('error', '請先選擇打印機');
    return;
  }

  if (selectedPrinter !== 'Zonerich AB-88H') {
    showAlert('error', '直接COM口打印僅支持Zonerich AB-88H打印機');
    return;
  }

  elements.directComLoading.classList.remove('hidden');
  elements.directComText.textContent = '正在打印...';
  elements.directComPrintBtn.disabled = true;

  try {
    showAlert('info', '正在通過COM3端口直接發送打印命令...');

    const printData = {
      message: elements.testMessage.value || '直接COM口打印測試成功！',
      timestamp: new Date().toISOString(),
      printer: selectedPrinter
    };

    const result = await ipcRenderer.invoke('direct-com-print', printData);

    if (result.success) {
      let successMessage = '🎉 直接COM口打印完成！\n\n';

      if (result.details) {
        const details = result.details;
        successMessage += `嘗試的方法: ${details.totalMethods || 0}\n`;
        successMessage += `成功的方法: ${details.successCount || 0}\n`;

        if (details.successfulMethods?.length > 0) {
          successMessage += `✅ 成功: ${details.successfulMethods.join(', ')}\n`;
        }

        if (details.failedMethods?.length > 0) {
          successMessage += `❌ 失敗: ${details.failedMethods.join(', ')}\n`;
        }

        successMessage += '\n🖨️ 請檢查打印機是否有紙張輸出！';

        // 顯示詳細結果
        console.log('直接COM口打印詳情:', details);
      }

      showAlert('success', successMessage);

      // 檢查打印機狀態
      setTimeout(() => {
        updatePrinterStatus();
      }, 2000);

    } else {
      showAlert('error', `直接COM口打印失敗: ${result.error}`);
      console.error('直接COM口打印失敗詳情:', result);
    }
  } catch (error) {
    showAlert('error', `直接COM口打印錯誤: ${error.message}`);
    console.error('直接COM口打印錯誤:', error);
  } finally {
    elements.directComLoading.classList.add('hidden');
    elements.directComText.textContent = '直接COM口打印';
    elements.directComPrintBtn.disabled = false;
  }
}

// Perform test print with enhanced debugging
async function performTestPrint() {
  elements.printLoading.classList.remove('hidden');
  elements.printText.textContent = 'Printing...';
  elements.testPrintBtn.disabled = true;

  try {
    // First test connectivity
    showAlert('info', 'Step 1: Testing printer connectivity...');
    const connectivityResult = await ipcRenderer.invoke('test-printer-connectivity', selectedPrinter);

    if (!connectivityResult.success) {
      throw new Error(`Connectivity test failed: ${connectivityResult.error}`);
    }

    console.log('Connectivity test passed:', connectivityResult);

    // Then attempt printing
    showAlert('info', 'Step 2: Sending print job...');
    const printData = {
      message: elements.testMessage.value || 'Test Print Successful!',
      timestamp: new Date().toISOString(),
      printer: selectedPrinter
    };

    const result = await ipcRenderer.invoke('test-print', printData);

    if (result.success) {
      let successMessage = '測試打印完成！';

      if (result.details) {
        const details = result.details;
        successMessage += `\n\n嘗試的方法: ${details.totalMethods || 0}`;
        successMessage += `\n成功的方法: ${details.successCount || 0}`;

        if (details.successfulMethods?.length > 0) {
          successMessage += `\n✅ 成功: ${details.successfulMethods.join(', ')}`;
        }

        if (details.failedMethods?.length > 0) {
          successMessage += `\n❌ 失敗: ${details.failedMethods.join(', ')}`;
        }

        // 顯示使用的端口信息
        if (details.port) {
          successMessage += `\n🔌 端口: ${details.port}`;
        }

        // Show detailed results in console for debugging
        console.log('打印作業詳情:', details);
      }

      showAlert('success', successMessage);

      // Check if printer actually received the job
      setTimeout(async () => {
        try {
          const postPrintCheck = await ipcRenderer.invoke('test-printer-connectivity', selectedPrinter);
          console.log('Post-print check:', postPrintCheck);

          if (postPrintCheck.success && postPrintCheck.jobCount > 0) {
            showAlert('info', `Print job queued! ${postPrintCheck.jobCount} job(s) in queue.`);
          } else {
            showAlert('info', 'Print command sent. Check printer for output.');
          }
        } catch (e) {
          console.log('Post-print check failed:', e);
        }

        updatePrinterStatus();
      }, 2000);

    } else {
      showAlert('error', `Print failed: ${result.error}`);
      console.error('Print failure details:', result.details);
    }
  } catch (error) {
    showAlert('error', `Test print failed: ${error.message}`);
    console.error('Test print error:', error);
  } finally {
    elements.printLoading.classList.add('hidden');
    elements.printText.textContent = 'Test Print';
    elements.testPrintBtn.disabled = false;
  }
}

// Update printer status
async function updatePrinterStatus() {
  try {
    const result = await ipcRenderer.invoke('get-printer-status');
    
    if (result.success) {
      // Update status display
      elements.statusPrinter.textContent = result.selectedPrinter || 'None selected';
      elements.statusText.textContent = result.status;
      elements.lastUpdated.textContent = new Date().toLocaleString();
      
      // Update connection status
      const connectionElement = elements.statusConnection;
      connectionElement.className = `status ${result.connected ? 'status-connected' : 'status-disconnected'}`;
      connectionElement.textContent = result.connected ? 'Connected' : 'Disconnected';
      
      // Update printer status in test print panel
      if (selectedPrinter) {
        const printerStatusElement = elements.printerStatus;
        printerStatusElement.className = `status ${result.connected ? 'status-connected' : 'status-disconnected'}`;
        printerStatusElement.textContent = result.connected ? 'Connected' : 'Disconnected';
        
        // Enable/disable test print button based on connection
        elements.testPrintBtn.disabled = !result.connected;
      }
    }
  } catch (error) {
    console.error('Failed to update printer status:', error);
  }
}

// Toggle auto-refresh
function toggleAutoRefresh() {
  if (isAutoRefreshing) {
    clearInterval(autoRefreshInterval);
    isAutoRefreshing = false;
    elements.autoRefreshBtn.textContent = 'Start Auto-refresh';
    elements.autoRefreshBtn.className = 'btn btn-primary';
    showAlert('info', 'Auto-refresh stopped');
  } else {
    autoRefreshInterval = setInterval(() => {
      updatePrinterStatus();
    }, 5000); // Update every 5 seconds
    isAutoRefreshing = true;
    elements.autoRefreshBtn.textContent = 'Stop Auto-refresh';
    elements.autoRefreshBtn.className = 'btn btn-secondary';
    showAlert('info', 'Auto-refresh started (every 5 seconds)');
  }
}

// Clean up on window close
window.addEventListener('beforeunload', () => {
  if (autoRefreshInterval) {
    clearInterval(autoRefreshInterval);
  }
});

console.log('Thermal Printer App JavaScript loaded successfully!');
