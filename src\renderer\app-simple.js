const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Application state
let printers = [];
let selectedPrinter = null;
let autoRefreshInterval = null;
let isAutoRefreshing = false;

// DOM elements
const elements = {
  alert: document.getElementById('alert'),
  alertMessage: document.getElementById('alert-message'),
  refreshBtn: document.getElementById('refresh-btn'),
  refreshLoading: document.getElementById('refresh-loading'),
  refreshText: document.getElementById('refresh-text'),
  noPrinters: document.getElementById('no-printers'),
  printerList: document.getElementById('printer-list'),
  noPrinterSelected: document.getElementById('no-printer-selected'),
  printerSelected: document.getElementById('printer-selected'),
  selectedPrinterName: document.getElementById('selected-printer-name'),
  printerStatus: document.getElementById('printer-status'),
  testMessage: document.getElementById('test-message'),
  testPrintBtn: document.getElementById('test-print-btn'),
  testConnectivityBtn: document.getElementById('test-connectivity-btn'),
  printLoading: document.getElementById('print-loading'),
  printText: document.getElementById('print-text'),
  autoRefreshBtn: document.getElementById('auto-refresh-btn'),
  statusPrinter: document.getElementById('status-printer'),
  statusConnection: document.getElementById('status-connection'),
  statusText: document.getElementById('status-text'),
  lastUpdated: document.getElementById('last-updated')
};

// Initialize application
document.addEventListener('DOMContentLoaded', async () => {
  console.log('Thermal Printer App loaded');
  
  // Set up event listeners
  elements.refreshBtn.addEventListener('click', refreshPrinters);
  elements.testPrintBtn.addEventListener('click', performTestPrint);
  elements.testConnectivityBtn.addEventListener('click', testConnectivity);
  elements.autoRefreshBtn.addEventListener('click', toggleAutoRefresh);
  
  // Initial load
  await refreshPrinters();
  await updatePrinterStatus();
  
  showAlert('success', 'Application initialized successfully');
});

// Show alert message
function showAlert(type, message) {
  elements.alert.className = `alert alert-${type}`;
  elements.alertMessage.textContent = message;
  elements.alert.classList.remove('hidden');
  
  // Auto-hide after 5 seconds for success/info, 8 seconds for errors
  const hideDelay = type === 'error' ? 8000 : 5000;
  setTimeout(() => {
    elements.alert.classList.add('hidden');
  }, hideDelay);
}

// Refresh printers list
async function refreshPrinters() {
  elements.refreshLoading.classList.remove('hidden');
  elements.refreshText.textContent = 'Refreshing...';
  elements.refreshBtn.disabled = true;
  
  try {
    const result = await ipcRenderer.invoke('get-printers');
    
    if (result.success) {
      printers = result.printers || [];
      selectedPrinter = result.selectedPrinter;
      
      renderPrinterList();
      updateSelectedPrinterUI();
      
      const printerCount = printers.length;
      const zonerichCount = printers.filter(p => p.isZonerich).length;
      
      let message = `Found ${printerCount} printer(s)`;
      if (zonerichCount > 0) {
        message += ` (${zonerichCount} Zonerich)`;
      }
      
      showAlert('success', message);
    } else {
      showAlert('error', `Failed to get printers: ${result.error}`);
    }
  } catch (error) {
    showAlert('error', `Failed to refresh printers: ${error.message}`);
  } finally {
    elements.refreshLoading.classList.add('hidden');
    elements.refreshText.textContent = 'Refresh Printers';
    elements.refreshBtn.disabled = false;
  }
}

// Render printer list
function renderPrinterList() {
  elements.printerList.innerHTML = '';
  
  if (printers.length === 0) {
    elements.noPrinters.classList.remove('hidden');
    return;
  }
  
  elements.noPrinters.classList.add('hidden');
  
  printers.forEach(printer => {
    const li = document.createElement('li');
    li.className = 'printer-item';
    
    if (printer.isSelected) {
      li.classList.add('selected');
    }
    
    if (printer.isZonerich) {
      li.classList.add('zonerich');
    }
    
    li.innerHTML = `
      <div class="printer-name">
        ${printer.name}
        ${printer.isZonerich ? '<span style="color: #28a745; font-weight: bold;">(Zonerich)</span>' : ''}
      </div>
      <div class="printer-details">
        Driver: ${printer.driver}<br>
        Port: ${printer.port}<br>
        Status: <span class="status status-${printer.status.toLowerCase().replace(' ', '-')}">${printer.status}</span>
      </div>
    `;
    
    li.addEventListener('click', () => selectPrinter(printer.name));
    elements.printerList.appendChild(li);
  });
}

// Select a printer
async function selectPrinter(printerName) {
  try {
    const result = await ipcRenderer.invoke('select-printer', printerName);
    
    if (result.success) {
      selectedPrinter = printerName;
      
      // Update the selected state in the printers list
      printers.forEach(printer => {
        printer.isSelected = printer.name === printerName;
      });
      
      renderPrinterList();
      updateSelectedPrinterUI();
      
      showAlert('success', `Printer "${printerName}" selected successfully`);
      
      // Update printer status after selection
      await updatePrinterStatus();
    } else {
      showAlert('error', `Failed to select printer: ${result.error}`);
    }
  } catch (error) {
    showAlert('error', `Failed to select printer: ${error.message}`);
  }
}

// Update selected printer UI
function updateSelectedPrinterUI() {
  if (selectedPrinter) {
    elements.noPrinterSelected.classList.add('hidden');
    elements.printerSelected.classList.remove('hidden');
    elements.selectedPrinterName.textContent = selectedPrinter;
  } else {
    elements.noPrinterSelected.classList.remove('hidden');
    elements.printerSelected.classList.add('hidden');
  }
}

// Test printer connectivity
async function testConnectivity() {
  if (!selectedPrinter) {
    showAlert('error', 'Please select a printer first');
    return;
  }

  try {
    showAlert('info', 'Testing printer connectivity...');
    const result = await ipcRenderer.invoke('test-printer-connectivity', selectedPrinter);

    if (result.success) {
      const details = [
        `Printer exists: ${result.printerExists ? 'Yes' : 'No'}`,
        `Status: ${result.printerStatus}`,
        `Jobs in queue: ${result.jobCount}`,
        `Queue info: ${result.queueInfo}`
      ].join('\n');

      showAlert('success', `Connectivity test passed!\n${details}`);
    } else {
      showAlert('error', `Connectivity test failed: ${result.error}`);
    }
  } catch (error) {
    showAlert('error', `Connectivity test error: ${error.message}`);
  }
}

// Perform test print with enhanced debugging
async function performTestPrint() {
  elements.printLoading.classList.remove('hidden');
  elements.printText.textContent = 'Printing...';
  elements.testPrintBtn.disabled = true;

  try {
    // First test connectivity
    showAlert('info', 'Step 1: Testing printer connectivity...');
    const connectivityResult = await ipcRenderer.invoke('test-printer-connectivity', selectedPrinter);

    if (!connectivityResult.success) {
      throw new Error(`Connectivity test failed: ${connectivityResult.error}`);
    }

    console.log('Connectivity test passed:', connectivityResult);

    // Then attempt printing
    showAlert('info', 'Step 2: Sending print job...');
    const printData = {
      message: elements.testMessage.value || 'Test Print Successful!',
      timestamp: new Date().toISOString(),
      printer: selectedPrinter
    };

    const result = await ipcRenderer.invoke('test-print', printData);

    if (result.success) {
      let successMessage = 'Test print completed!';

      if (result.details) {
        const details = result.details;
        successMessage += `\n\nMethods tried: ${details.methods?.length || 0}`;

        if (details.successfulMethods?.length > 0) {
          successMessage += `\nSuccessful: ${details.successfulMethods.join(', ')}`;
        }

        if (details.failedMethods?.length > 0) {
          successMessage += `\nFailed: ${details.failedMethods.join(', ')}`;
        }

        // Show detailed results in console for debugging
        console.log('Print job details:', details);
      }

      showAlert('success', successMessage);

      // Check if printer actually received the job
      setTimeout(async () => {
        try {
          const postPrintCheck = await ipcRenderer.invoke('test-printer-connectivity', selectedPrinter);
          console.log('Post-print check:', postPrintCheck);

          if (postPrintCheck.success && postPrintCheck.jobCount > 0) {
            showAlert('info', `Print job queued! ${postPrintCheck.jobCount} job(s) in queue.`);
          } else {
            showAlert('info', 'Print command sent. Check printer for output.');
          }
        } catch (e) {
          console.log('Post-print check failed:', e);
        }

        updatePrinterStatus();
      }, 2000);

    } else {
      showAlert('error', `Print failed: ${result.error}`);
      console.error('Print failure details:', result.details);
    }
  } catch (error) {
    showAlert('error', `Test print failed: ${error.message}`);
    console.error('Test print error:', error);
  } finally {
    elements.printLoading.classList.add('hidden');
    elements.printText.textContent = 'Test Print';
    elements.testPrintBtn.disabled = false;
  }
}

// Update printer status
async function updatePrinterStatus() {
  try {
    const result = await ipcRenderer.invoke('get-printer-status');
    
    if (result.success) {
      // Update status display
      elements.statusPrinter.textContent = result.selectedPrinter || 'None selected';
      elements.statusText.textContent = result.status;
      elements.lastUpdated.textContent = new Date().toLocaleString();
      
      // Update connection status
      const connectionElement = elements.statusConnection;
      connectionElement.className = `status ${result.connected ? 'status-connected' : 'status-disconnected'}`;
      connectionElement.textContent = result.connected ? 'Connected' : 'Disconnected';
      
      // Update printer status in test print panel
      if (selectedPrinter) {
        const printerStatusElement = elements.printerStatus;
        printerStatusElement.className = `status ${result.connected ? 'status-connected' : 'status-disconnected'}`;
        printerStatusElement.textContent = result.connected ? 'Connected' : 'Disconnected';
        
        // Enable/disable test print button based on connection
        elements.testPrintBtn.disabled = !result.connected;
      }
    }
  } catch (error) {
    console.error('Failed to update printer status:', error);
  }
}

// Toggle auto-refresh
function toggleAutoRefresh() {
  if (isAutoRefreshing) {
    clearInterval(autoRefreshInterval);
    isAutoRefreshing = false;
    elements.autoRefreshBtn.textContent = 'Start Auto-refresh';
    elements.autoRefreshBtn.className = 'btn btn-primary';
    showAlert('info', 'Auto-refresh stopped');
  } else {
    autoRefreshInterval = setInterval(() => {
      updatePrinterStatus();
    }, 5000); // Update every 5 seconds
    isAutoRefreshing = true;
    elements.autoRefreshBtn.textContent = 'Stop Auto-refresh';
    elements.autoRefreshBtn.className = 'btn btn-secondary';
    showAlert('info', 'Auto-refresh started (every 5 seconds)');
  }
}

// Clean up on window close
window.addEventListener('beforeunload', () => {
  if (autoRefreshInterval) {
    clearInterval(autoRefreshInterval);
  }
});

console.log('Thermal Printer App JavaScript loaded successfully!');
