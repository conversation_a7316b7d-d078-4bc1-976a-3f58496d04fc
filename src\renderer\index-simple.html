<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Thermal Printer App</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #f8f9fa;
      color: #333;
      line-height: 1.6;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px 0;
      margin-bottom: 30px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
    }

    .header h1 {
      font-size: 2.5rem;
      font-weight: 300;
      margin: 0;
    }

    .header p {
      margin-top: 10px;
      opacity: 0.9;
    }

    .card {
      background: white;
      border-radius: 12px;
      padding: 25px;
      margin-bottom: 25px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
      border: 1px solid #e9ecef;
    }

    .card h2 {
      color: #495057;
      margin-bottom: 20px;
      font-size: 1.5rem;
      font-weight: 500;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      text-decoration: none;
      display: inline-block;
      text-align: center;
      margin: 5px;
    }

    .btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
      color: white;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .status {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .status-connected {
      background: #d4edda;
      color: #155724;
    }

    .status-disconnected {
      background: #f8d7da;
      color: #721c24;
    }

    .status-ready {
      background: #d1ecf1;
      color: #0c5460;
    }

    .printer-list {
      list-style: none;
    }

    .printer-item {
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .printer-item:hover {
      border-color: #667eea;
      background: #f0f4ff;
    }

    .printer-item.selected {
      border-color: #667eea;
      background: #e7f3ff;
    }

    .printer-item.zonerich {
      border-color: #28a745;
      background: #f0fff4;
    }

    .printer-item.zonerich.selected {
      border-color: #28a745;
      background: #e6ffed;
    }

    .printer-name {
      font-weight: 600;
      color: #495057;
      margin-bottom: 5px;
    }

    .printer-details {
      font-size: 12px;
      color: #6c757d;
    }

    .alert {
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      border: 1px solid transparent;
    }

    .alert-success {
      background: #d4edda;
      border-color: #c3e6cb;
      color: #155724;
    }

    .alert-error {
      background: #f8d7da;
      border-color: #f5c6cb;
      color: #721c24;
    }

    .alert-info {
      background: #d1ecf1;
      border-color: #bee5eb;
      color: #0c5460;
    }

    .grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 25px;
    }

    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .hidden {
      display: none;
    }

    input[type="text"] {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 15px;
    }

    @media (max-width: 768px) {
      .grid {
        grid-template-columns: 1fr;
      }
      
      .container {
        padding: 15px;
      }
      
      .header h1 {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="container">
      <h1>Thermal Printer Manager</h1>
      <p>Zonerich AB-88H Integration</p>
    </div>
  </div>

  <div class="container">
    <!-- Status Alert -->
    <div id="alert" class="alert hidden">
      <span id="alert-message"></span>
    </div>

    <div class="grid">
      <!-- Printer Selection Panel -->
      <div class="card">
        <h2>Printer Selection</h2>
        
        <div style="margin-bottom: 20px;">
          <button id="refresh-btn" class="btn btn-secondary">
            <span id="refresh-loading" class="loading hidden"></span>
            <span id="refresh-text">Refresh Printers</span>
          </button>
        </div>

        <div id="no-printers" class="alert alert-info hidden">
          No printers found. Make sure your Zonerich AB-88H printer is connected and installed.
        </div>

        <ul id="printer-list" class="printer-list"></ul>
      </div>

      <!-- Test Print Panel -->
      <div class="card">
        <h2>Test Print</h2>
        
        <div id="no-printer-selected" class="alert alert-info">
          Please select a printer first to enable test printing.
        </div>

        <div id="printer-selected" class="hidden">
          <div style="margin-bottom: 20px;">
            <p><strong>Selected Printer:</strong> <span id="selected-printer-name"></span></p>
            <p><strong>Status:</strong>
              <span id="printer-status" class="status status-disconnected">Disconnected</span>
            </p>
          </div>

          <div style="margin-bottom: 20px;">
            <button id="test-connectivity-btn" class="btn btn-secondary" style="width: 100%; margin-bottom: 10px;">
              測試打印機連接
            </button>
            <button id="get-serial-ports-btn" class="btn btn-secondary" style="width: 100%; margin-bottom: 10px;">
              檢測串口設備
            </button>
            <button id="direct-com-print-btn" class="btn btn-primary" style="width: 100%; margin-bottom: 10px;">
              <span id="direct-com-loading" class="loading hidden"></span>
              <span id="direct-com-text">直接COM口打印</span>
            </button>
          </div>

          <div id="connection-info" style="margin-bottom: 20px; padding: 10px; background: #f8f9fa; border-radius: 4px; display: none;">
            <h4 style="margin: 0 0 10px 0; color: #495057;">連接信息：</h4>
            <div id="connection-details"></div>
          </div>

          <div style="margin-bottom: 20px;">
            <label for="test-message" style="display: block; margin-bottom: 8px; font-weight: 500;">
              Test Message:
            </label>
            <input 
              id="test-message"
              type="text" 
              placeholder="Enter custom test message..."
              value="Hello from Thermal Printer!"
            >
          </div>

          <button id="test-print-btn" class="btn btn-success" style="width: 100%;">
            <span id="print-loading" class="loading hidden"></span>
            <span id="print-text">Test Print</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Printer Status Panel -->
    <div class="card">
      <h2>Printer Status</h2>
      
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
        <span><strong>Auto-refresh status:</strong></span>
        <button id="auto-refresh-btn" class="btn btn-primary">
          Start Auto-refresh
        </button>
      </div>

      <div id="status-info">
        <p><strong>Printer:</strong> <span id="status-printer">None selected</span></p>
        <p><strong>Connection:</strong> 
          <span id="status-connection" class="status status-disconnected">Disconnected</span>
        </p>
        <p><strong>Status:</strong> <span id="status-text">Unknown</span></p>
        <p><strong>Last Updated:</strong> <span id="last-updated">Never</span></p>
      </div>
    </div>
  </div>

  <script src="app-simple.js"></script>
</body>
</html>
