// 簡單的COM3測試工具
const { SerialPort } = require('serialport');

console.log('🔧 測試COM3端口連接...\n');

// 創建串口實例
const port = new SerialPort({
  path: 'COM3',
  baudRate: 9600,
  dataBits: 8,
  stopBits: 1,
  parity: 'none',
  autoOpen: false
});

// 嘗試打開端口
port.open((error) => {
  if (error) {
    console.log('❌ COM3端口打開失敗:');
    console.log('   錯誤:', error.message);
    console.log('   錯誤代碼:', error.errno || 'N/A');
    console.log('\n可能的解決方案:');
    console.log('1. 確保沒有其他程序使用COM3端口');
    console.log('2. 重新啟動計算機');
    console.log('3. 檢查設備管理器中的COM3端口狀態');
    console.log('4. 嘗試使用不同的波特率');
    
    process.exit(1);
  } else {
    console.log('✅ COM3端口打開成功！');
    
    // 發送測試數據
    const testData = 'Hello Zonerich!\n';
    port.write(testData, (writeError) => {
      if (writeError) {
        console.log('❌ 數據發送失敗:', writeError.message);
      } else {
        console.log('✅ 測試數據發送成功');
        console.log('   發送內容:', testData.trim());
      }
      
      // 關閉端口
      port.close((closeError) => {
        if (closeError) {
          console.log('⚠️ 端口關閉時出現警告:', closeError.message);
        } else {
          console.log('✅ COM3端口已正常關閉');
        }
        
        console.log('\n🎯 COM3端口測試完成！');
        process.exit(0);
      });
    });
  }
});

// 設置超時
setTimeout(() => {
  console.log('⏰ 測試超時，強制退出');
  process.exit(1);
}, 10000);
