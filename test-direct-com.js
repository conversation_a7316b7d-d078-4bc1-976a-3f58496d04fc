// 獨立的直接COM口測試工具
const DirectCOMPrinter = require('./src/main/direct-com-printer');

console.log('🔧 直接COM口打印測試工具');
console.log('========================================\n');

async function runTest() {
  const printer = new DirectCOMPrinter();
  
  const testData = {
    message: '獨立測試：直接COM口通信成功！',
    timestamp: new Date().toISOString()
  };
  
  console.log('開始測試直接COM口打印...\n');
  
  try {
    const result = await printer.testPrint(testData);
    
    if (result.success) {
      console.log('✅ 測試成功！');
      console.log('消息:', result.message);
      console.log('時間戳:', result.timestamp);
      
      if (result.details) {
        console.log('\n詳細結果:');
        console.log('- 總方法數:', result.details.totalMethods);
        console.log('- 成功方法數:', result.details.successCount);
        console.log('- 成功的方法:', result.details.successfulMethods);
        console.log('- 失敗的方法:', result.details.failedMethods);
        
        console.log('\n各方法詳情:');
        result.details.results.forEach((methodResult, index) => {
          const status = methodResult.success ? '✅' : '❌';
          console.log(`${index + 1}. ${status} ${methodResult.method}`);
          if (methodResult.error) {
            console.log(`   錯誤: ${methodResult.error}`);
          }
          if (methodResult.message) {
            console.log(`   消息: ${methodResult.message}`);
          }
        });
      }
      
      console.log('\n🖨️ 請檢查您的Zonerich AB-88H打印機是否有紙張輸出！');
      
    } else {
      console.log('❌ 測試失敗！');
      console.log('錯誤:', result.error);
      
      if (result.details) {
        console.log('\n失敗詳情:');
        result.details.results.forEach((methodResult, index) => {
          console.log(`${index + 1}. ❌ ${methodResult.method}: ${methodResult.error}`);
        });
      }
    }
    
  } catch (error) {
    console.log('❌ 測試過程中發生錯誤:');
    console.log(error.message);
    console.log('\n可能的原因:');
    console.log('1. COM3端口被其他程序占用');
    console.log('2. 需要管理員權限');
    console.log('3. 打印機未正確連接');
    console.log('4. 打印機驅動程序問題');
  }
}

// 運行測試
runTest().then(() => {
  console.log('\n========================================');
  console.log('測試完成！');
  process.exit(0);
}).catch((error) => {
  console.error('測試失敗:', error);
  process.exit(1);
});
