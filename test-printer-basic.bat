@echo off
echo Testing Zonerich AB-88H Printer Connectivity
echo ============================================
echo.

echo Step 1: Checking available printers...
powershell "Get-Printer | Where-Object {$_.Name -like '*Zonerich*' -or $_.Name -like '*AB-88H*'} | Select-Object Name, PrinterStatus, DriverName, PortName | Format-Table -AutoSize"

echo.
echo Step 2: Creating test file...
echo BASIC PRINTER TEST > test-print.txt
echo ================== >> test-print.txt
echo Date: %date% %time% >> test-print.txt
echo This is a basic connectivity test >> test-print.txt
echo If you can see this, the printer is working >> test-print.txt
echo ================== >> test-print.txt
echo END OF TEST >> test-print.txt

echo.
echo Step 3: Attempting to print test file...
echo Please check your printer for output.
echo.

REM Try to find <PERSON>rich printer and print to it
for /f "tokens=*" %%i in ('powershell "Get-Printer | Where-Object {$_.Name -like '*Zonerich*' -or $_.Name -like '*AB-88H*'} | Select-Object -First 1 -ExpandProperty Name"') do (
    echo Found printer: %%i
    echo Sending test file to printer...
    print /D:"%%i" test-print.txt
    if errorlevel 1 (
        echo ERROR: Print command failed
    ) else (
        echo Print command executed successfully
    )
)

echo.
echo Step 4: Checking print queue...
powershell "Get-PrintJob | Where-Object {$_.PrinterName -like '*Zonerich*' -or $_.PrinterName -like '*AB-88H*'} | Select-Object PrinterName, JobStatus, TotalPages | Format-Table -AutoSize"

echo.
echo Step 5: Alternative method - PowerShell Out-Printer...
for /f "tokens=*" %%i in ('powershell "Get-Printer | Where-Object {$_.Name -like '*Zonerich*' -or $_.Name -like '*AB-88H*'} | Select-Object -First 1 -ExpandProperty Name"') do (
    echo Trying PowerShell method with printer: %%i
    powershell "Get-Content test-print.txt | Out-Printer -Name '%%i'"
    if errorlevel 1 (
        echo ERROR: PowerShell print failed
    ) else (
        echo PowerShell print executed successfully
    )
)

echo.
echo Test completed. Check your printer for output.
echo If nothing printed, there may be a driver or connection issue.
echo.
pause

REM Clean up
del test-print.txt 2>nul
